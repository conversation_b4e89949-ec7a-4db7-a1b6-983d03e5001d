#!/bin/bash

# Format script for stock analysis project
# Formats backend Python code with black and frontend JavaScript/React code with prettier

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to format backend
format_backend() {
    print_status "Formatting backend Python code with black..."

    if [ ! -d "backend" ]; then
        print_error "Backend directory not found!"
        return 1
    fi

    cd backend

    # Check if black is available
    if ! command_exists black; then
        print_warning "Black not found. Installing black..."
        if command_exists pip; then
            pip install black
        else
            print_error "pip not found. Please install black manually: pip install black"
            cd ..
            return 1
        fi
    fi

    # Format with black using configuration from pyproject.toml
    print_status "Running black on backend code..."
    if [ "$FORCE_FORMAT" = true ]; then
        print_status "Force formatting backend code..."
        if black src/ tests/; then
            print_success "Backend code formatted successfully"
        else
            print_error "Failed to format backend code with black"
            cd ..
            return 1
        fi
    elif black src/ tests/ --check --diff 2>/dev/null; then
        print_success "Backend code is already formatted correctly"
    else
        print_status "Formatting backend code..."
        if black src/ tests/; then
            print_success "Backend code formatted successfully"
        else
            print_error "Failed to format backend code with black"
            cd ..
            return 1
        fi
    fi

    # Also run isort if available
    if command_exists isort; then
        print_status "Running isort on backend code..."
        if [ "$FORCE_FORMAT" = true ]; then
            print_status "Force sorting backend imports..."
            if isort src/ tests/; then
                print_success "Backend imports sorted successfully"
            else
                print_error "Failed to sort backend imports with isort"
                cd ..
                return 1
            fi
        elif isort src/ tests/ --check-only --diff 2>/dev/null; then
            print_success "Backend imports are already sorted correctly"
        else
            print_status "Sorting backend imports..."
            if isort src/ tests/; then
                print_success "Backend imports sorted successfully"
            else
                print_error "Failed to sort backend imports with isort"
                cd ..
                return 1
            fi
        fi
    else
        print_warning "isort not found. Consider installing it: pip install isort"
    fi
    
    cd ..
}

# Function to format frontend
format_frontend() {
    print_status "Formatting frontend JavaScript/React code with prettier..."
    
    if [ ! -d "frontend" ]; then
        print_error "Frontend directory not found!"
        return 1
    fi
    
    cd frontend
    
    # Check if npm is available
    if ! command_exists npm; then
        print_error "npm not found. Please install Node.js and npm"
        cd ..
        return 1
    fi
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules not found. Installing dependencies..."
        npm install
    fi
    
    # Format frontend code (runs linting and prettier)
    print_status "Formatting frontend code with linting and prettier..."
    npm run format
    if [ $? -eq 0 ]; then
        print_success "Frontend code formatted successfully"
    else
        print_error "Failed to format frontend code"
        cd ..
        return 1
    fi

    # Note: npm run format now includes both linting (lint:fix) and prettier formatting
    
    cd ..
}

# Main function
main() {
    print_status "Starting code formatting for stock analysis project..."
    echo
    
    # Parse command line arguments
    LINT_FRONTEND=false
    BACKEND_ONLY=false
    FRONTEND_ONLY=false
    FORCE_FORMAT=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --lint)
                LINT_FRONTEND=true
                shift
                ;;
            --backend-only)
                BACKEND_ONLY=true
                shift
                ;;
            --frontend-only)
                FRONTEND_ONLY=true
                shift
                ;;
            --fix)
                FORCE_FORMAT=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo
                echo "Options:"
                echo "  --lint           Also run ESLint on frontend code"
                echo "  --backend-only   Format only backend code"
                echo "  --frontend-only  Format only frontend code"
                echo "  --fix            Force formatting even if code appears formatted"
                echo "  --help, -h       Show this help message"
                echo
                echo "Examples:"
                echo "  $0                    # Format both backend and frontend"
                echo "  $0 --lint            # Format both and run frontend linting"
                echo "  $0 --backend-only    # Format only backend"
                echo "  $0 --frontend-only   # Format only frontend"
                echo "  $0 --fix             # Force format both backend and frontend"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Format based on options
    if [ "$FRONTEND_ONLY" = true ]; then
        if [ "$LINT_FRONTEND" = true ]; then
            format_frontend --lint
        else
            format_frontend
        fi
    elif [ "$BACKEND_ONLY" = true ]; then
        format_backend
    else
        # Format both
        format_backend
        echo
        if [ "$LINT_FRONTEND" = true ]; then
            format_frontend --lint
        else
            format_frontend
        fi
    fi
    
    echo
    print_success "Code formatting completed successfully!"
}

# Run main function with all arguments
main "$@"
