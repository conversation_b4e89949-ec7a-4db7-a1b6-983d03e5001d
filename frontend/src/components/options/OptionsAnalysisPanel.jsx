import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material';
import { PageCard, GradientTypography } from '../../styles/common';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Settings as SettingsIcon,
  PlayArrow as PlayArrowIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ExpandMore as ExpandMoreIcon,
  Assessment as AssessmentIcon,
  Security as SecurityIcon,
  AccountBalance as AccountBalanceIcon,
} from '@mui/icons-material';
import { toast } from 'react-hot-toast';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';
import WatchlistDialog from './WatchlistDialog';
import StrategyConfigDialog from './StrategyConfigDialog';

const OptionsAnalysisPanel = ({ selectedAccount }) => {
  // State management
  const [selectedStrategy, setSelectedStrategy] = useState(
    'cash_covered_options'
  );
  const [watchlists, setWatchlists] = useState([]);
  const [selectedWatchlist, setSelectedWatchlist] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [marketConditions, setMarketConditions] = useState(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [watchlistDialogOpen, setWatchlistDialogOpen] = useState(false);
  const [strategyConfig, setStrategyConfig] = useState({});
  const [selectedRowIndex, setSelectedRowIndex] = useState(null);

  // Hooks for sticky column behavior - moved to top level to avoid hook order issues
  const tableContainerRef = useRef(null);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll events to provide visual feedback for sticky column
  const handleScroll = useCallback(event => {
    const scrollLeft = event.target.scrollLeft;
    setIsScrolled(scrollLeft > 0);
  }, []);

  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // Strategy options - will be loaded from API
  const [strategyOptions, setStrategyOptions] = useState([
    {
      value: 'cash_covered_options',
      label: 'Cash-Covered Options (现金担保期权)',
      icon: <SecurityIcon />,
    },
    {
      value: 'iron_condors',
      label: 'Iron Condors (铁鹰策略)',
      icon: <AssessmentIcon />,
    },
  ]);

  // Load supported strategies on mount
  useEffect(() => {
    loadSupportedStrategies();
  }, []);

  // Load watchlists and default config on mount
  useEffect(() => {
    if (selectedAccount) {
      loadWatchlists();
      loadStrategyConfig();
    }
  }, [selectedAccount, selectedStrategy]);

  // Load supported strategies from API
  const loadSupportedStrategies = async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_SUPPORTED_STRATEGIES}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch supported strategies');
      }
      const data = await response.json();

      // Map API response to component format
      const mappedStrategies = data.strategies.map(strategy => ({
        value: strategy.id,
        label: `${strategy.name} (${strategy.display_name})`,
        icon:
          strategy.id === 'cash_covered_options' ? (
            <SecurityIcon />
          ) : (
            <AssessmentIcon />
          ),
      }));

      setStrategyOptions(mappedStrategies);
    } catch (error) {
      console.error('Error loading supported strategies:', error);
      // Keep default strategies if API fails
    }
  };

  const loadWatchlists = async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_WATCHLISTS}?account_id=${selectedAccount}`
      );
      const data = await response.json();

      if (response.ok) {
        setWatchlists(data.watchlists || []);
        if (data.watchlists && data.watchlists.length > 0) {
          setSelectedWatchlist(data.watchlists[0]);
        }
      } else {
        toast.error(`加载观察列表失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`加载观察列表失败: ${error.message}`);
    }
  };

  const loadStrategyConfig = async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_CONFIG}/${selectedStrategy}?account_id=${selectedAccount}`
      );
      const data = await response.json();

      if (response.ok) {
        setStrategyConfig(data.config || {});
      } else {
        toast.error(`加载策略配置失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`加载策略配置失败: ${error.message}`);
    }
  };

  const runAnalysis = async () => {
    if (
      !selectedWatchlist ||
      !selectedWatchlist.symbols ||
      selectedWatchlist.symbols.length === 0
    ) {
      toast.error('请先选择包含股票的观察列表');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisResults(null);
    setSelectedRowIndex(null); // Clear selection when starting new analysis

    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_ANALYZE}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            account_id: selectedAccount,
            strategy_type: selectedStrategy,
            symbols: selectedWatchlist.symbols,
            config: strategyConfig,
          }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        setAnalysisResults(data);
        setMarketConditions(data.market_conditions);
        toast.success('期权分析完成！');
      } else {
        toast.error(`分析失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`分析失败: ${error.message}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderStrategySelector = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography
          variant="h6"
          gutterBottom
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <SecurityIcon sx={{ mr: 1 }} />
          策略选择
        </Typography>
        <FormControl fullWidth>
          <InputLabel>选择期权策略</InputLabel>
          <Select
            value={selectedStrategy}
            onChange={e => setSelectedStrategy(e.target.value)}
            label="选择期权策略"
          >
            {strategyOptions.map(option => (
              <MenuItem key={option.value} value={option.value}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {option.icon}
                  <Typography sx={{ ml: 1 }}>{option.label}</Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </CardContent>
    </Card>
  );

  const renderWatchlistSelector = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Typography
            variant="h6"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <AccountBalanceIcon sx={{ mr: 1 }} />
            观察列表
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={() => {
              if (!selectedAccount) {
                toast.error('请先选择账户');
                return;
              }
              setWatchlistDialogOpen(true);
            }}
            size="small"
          >
            管理列表
          </Button>
        </Box>

        {watchlists.length > 0 ? (
          <FormControl fullWidth>
            <InputLabel>选择观察列表</InputLabel>
            <Select
              value={selectedWatchlist?.watchlist_id || ''}
              onChange={e => {
                const watchlist = watchlists.find(
                  w => w.watchlist_id === e.target.value
                );
                setSelectedWatchlist(watchlist);
              }}
              label="选择观察列表"
            >
              {watchlists.map(watchlist => (
                <MenuItem
                  key={watchlist.watchlist_id}
                  value={watchlist.watchlist_id}
                >
                  <Box>
                    <Typography variant="body1">{watchlist.name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {watchlist.symbols.length} 只股票
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        ) : (
          <Alert severity="info">暂无观察列表，请先创建一个观察列表</Alert>
        )}

        {selectedWatchlist && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              当前列表股票:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {selectedWatchlist.symbols.map(symbol => (
                <Chip key={symbol} label={symbol} size="small" />
              ))}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const renderAnalysisControls = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant="h6">分析控制</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<SettingsIcon />}
              onClick={() => setConfigDialogOpen(true)}
              variant="outlined"
              size="small"
            >
              配置参数
            </Button>
            <Button
              startIcon={
                isAnalyzing ? <CircularProgress size={16} /> : <PlayArrowIcon />
              }
              onClick={runAnalysis}
              variant="contained"
              disabled={isAnalyzing || !selectedWatchlist}
            >
              {isAnalyzing ? '分析中...' : '开始分析'}
            </Button>
          </Box>
        </Box>

        {marketConditions && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              市场状况:
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Chip
                label={`波动率: ${marketConditions.volatility_regime}`}
                color={
                  marketConditions.volatility_regime === 'High Volatility'
                    ? 'error'
                    : marketConditions.volatility_regime === 'Low Volatility'
                      ? 'success'
                      : 'default'
                }
                size="small"
              />
              <Chip
                label={`压力指数: ${marketConditions.stress_indicator?.toFixed(1) || 'N/A'}`}
                color={
                  marketConditions.stress_indicator > 75
                    ? 'error'
                    : marketConditions.stress_indicator < 25
                      ? 'success'
                      : 'warning'
                }
                size="small"
              />
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const renderAnalysisResults = () => {
    if (!analysisResults) return null;

    // Handle unified cash-covered options response format
    if (selectedStrategy === 'cash_covered_options') {
      return (
        <PageCard>
          <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
            <GradientTypography
              variant="h5"
              gutterBottom
              sx={{ mb: 4, textAlign: 'center' }}
            >
              现金担保期权分析结果
            </GradientTypography>
            {/* Cash-Secured Puts Section */}
            {analysisResults.cash_secured_puts && (
              <Accordion
                defaultExpanded
                sx={{
                  mb: 3,
                  borderRadius: '8px',
                  border: '1px solid rgba(224, 224, 224, 0.5)',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  '&:before': { display: 'none' },
                  overflow: 'hidden',
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    backgroundColor: '#fafafa',
                    borderRadius: '8px 8px 0 0',
                    minHeight: '64px',
                    '&.Mui-expanded': {
                      borderRadius: '8px 8px 0 0',
                      minHeight: '64px',
                    },
                    '& .MuiAccordionSummary-content': {
                      alignItems: 'center',
                      gap: 2,
                      margin: '16px 0',
                    },
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 3,
                      width: '100%',
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: '700',
                        fontSize: '1.2rem',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                      }}
                    >
                      📉 Cash-Secured Puts
                      <Typography
                        component="span"
                        sx={{
                          color: '#64748B',
                          fontWeight: '500',
                          fontSize: '0.9rem',
                          ml: 1,
                        }}
                      >
                        (现金担保看跌期权)
                      </Typography>
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails
                  sx={{
                    p: 0,
                    backgroundColor: '#ffffff',
                    borderRadius: '0 0 8px 8px',
                  }}
                >
                  {renderCandidatesTable(
                    analysisResults.cash_secured_puts.candidates,
                    'puts'
                  )}
                </AccordionDetails>
              </Accordion>
            )}

            {/* Covered Calls Section */}
            {analysisResults.covered_calls && (
              <Accordion
                sx={{
                  mb: 3,
                  borderRadius: '8px',
                  border: '1px solid rgba(224, 224, 224, 0.5)',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  '&:before': { display: 'none' },
                  overflow: 'hidden',
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    backgroundColor: '#fafafa',
                    borderRadius: '8px 8px 0 0',
                    minHeight: '64px',
                    '&.Mui-expanded': {
                      borderRadius: '8px 8px 0 0',
                      minHeight: '64px',
                    },
                    '& .MuiAccordionSummary-content': {
                      alignItems: 'center',
                      gap: 2,
                      margin: '16px 0',
                    },
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 3,
                      width: '100%',
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: '700',
                        fontSize: '1.2rem',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                      }}
                    >
                      📈 Covered Calls
                      <Typography
                        component="span"
                        sx={{
                          color: '#64748B',
                          fontWeight: '500',
                          fontSize: '0.9rem',
                          ml: 1,
                        }}
                      >
                        (备兑看涨期权)
                      </Typography>
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails
                  sx={{
                    p: 0,
                    backgroundColor: '#ffffff',
                    borderRadius: '0 0 8px 8px',
                  }}
                >
                  {renderCandidatesTable(
                    analysisResults.covered_calls.candidates,
                    'calls'
                  )}
                </AccordionDetails>
              </Accordion>
            )}
          </CardContent>
        </PageCard>
      );
    }

    // Handle traditional iron condors response format
    return (
      <PageCard>
        <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
          <GradientTypography
            variant="h5"
            gutterBottom
            sx={{ mb: 4, textAlign: 'center' }}
          >
            分析结果
          </GradientTypography>

          <Box sx={{ mt: 2 }}>
            {renderCandidatesTable(
              analysisResults.candidates,
              selectedStrategy
            )}
          </Box>
        </CardContent>
      </PageCard>
    );
  };

  // Helper function to calculate missing fields
  const enhanceCandidate = candidate => {
    const enhanced = { ...candidate };

    // Calculate missing fields if not present
    if (!enhanced.totalReturn && enhanced.premium && enhanced.strike) {
      // Total return = premium for options
      enhanced.totalReturn = enhanced.premium;
    }

    if (!enhanced.roiPercent && enhanced.annualizedRoi) {
      // ROI % is the same as annualized ROI
      enhanced.roiPercent = enhanced.annualizedRoi;
    }

    // Opportunity cost is now calculated by the backend for both puts and calls
    // No fallback calculation needed

    if (!enhanced.maxGain && enhanced.premium) {
      // Max gain for options is typically the premium received
      enhanced.maxGain = enhanced.premium * 100; // 100 shares per contract
    }

    return enhanced;
  };

  const renderCandidatesTable = (candidates, type) => {
    if (!candidates || candidates.length === 0) {
      return <Alert severity="info">没有找到符合条件的候选项</Alert>;
    }

    // Enhance candidates with calculated fields
    const enhancedCandidates = candidates.map(enhanceCandidate);

    // Define columns based on strategy type
    const getColumns = () => {
      // Comprehensive columns for options analysis
      const comprehensiveColumns = [
        'symbol', // Symbol (股票代码)
        'currentPrice', // Cur. Price (当前价格)
        'strike', // Strike (行权价)
        'expiration', // Expires (到期日)
        'dte', // DTE (剩余天数)
        'bid', // Bid (买价)
        'premium', // Premium (权利金)
        'annualizedRoi', // Ann. ROI (年化收益率)
        'upsideBufferPercent', // Upside % (上涨空间) - for calls
        'totalReturn', // Total Ret. (总收益)
        'winRateScore', // Win Score (胜率评分)
        'impliedVolatility', // IV (隐含波动率)
        'roiPercent', // ROI % (投资回报率)
        'opportunityCost', // Opp. Cost (机会成本)
        'maxGain', // Max Gain (最大收益)
      ];

      if (type === 'puts') {
        // For puts, replace upsideBufferPercent with bufferPercent
        return comprehensiveColumns.map(col =>
          col === 'upsideBufferPercent' ? 'bufferPercent' : col
        );
      } else if (type === 'calls') {
        // For calls, keep upsideBufferPercent
        return comprehensiveColumns;
      } else if (selectedStrategy === 'iron_condors') {
        return [
          'symbol',
          'currentPrice',
          'dte',
          'netPremium',
          'maxProfit',
          'maxLoss',
          'probabilityOfProfit',
          'annualizedRoi',
          'winRateScore',
        ];
      }

      // Default comprehensive columns
      return comprehensiveColumns;
    };

    const columns = getColumns();

    return (
      <TableContainer
        ref={tableContainerRef}
        sx={{
          maxHeight: { xs: 500, sm: 600, md: 700, lg: 800 },
          overflow: 'auto',
          position: 'relative',
          border: '1px solid rgba(224, 224, 224, 0.5)',
          borderRadius: '8px',
          // Enhanced scrollbar styling
          '&::-webkit-scrollbar': {
            width: '12px',
            height: '12px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: '#f5f5f5',
            borderRadius: '6px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#bdbdbd',
            borderRadius: '6px',
            '&:hover': {
              backgroundColor: '#9e9e9e',
            },
          },
          '&::-webkit-scrollbar-corner': {
            backgroundColor: '#f5f5f5',
          },
          // Smooth scrolling
          scrollBehavior: 'smooth',
          // Improved table cell styling
          '& .MuiTableCell-root': {
            fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.9rem' },
            padding: { xs: '8px 12px', sm: '10px 14px', md: '12px 16px' },
            whiteSpace: 'nowrap',
            fontFamily:
              '"Roboto", "Helvetica", "Arial", "Noto Sans SC", "Microsoft YaHei", sans-serif',
            borderRight: '1px solid rgba(224, 224, 224, 0.3)',
            textAlign: 'left',
            verticalAlign: 'middle',
            '&:last-child': {
              borderRight: 'none',
            },
          },
          // Table layout optimization
          '& .MuiTable-root': {
            borderCollapse: 'separate',
            borderSpacing: 0,
            minWidth: {
              xs: '1200px',
              sm: '1400px',
              md: '1600px',
              lg: '1800px',
            },
            tableLayout: 'auto',
            width: 'max-content',
          },
        }}
      >
        <Table size="small" stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((column, colIndex) => (
                <TableCell
                  key={column}
                  sx={{
                    minWidth:
                      colIndex === 0 ? '160px' : getColumnMinWidth(column),
                    maxWidth:
                      colIndex === 0 ? '160px' : getColumnMaxWidth(column),
                    width: colIndex === 0 ? '160px' : 'auto',
                    position: 'sticky',
                    top: 0,
                    fontWeight: 'bold',
                    backgroundColor: '#fafafa',
                    borderBottom: '2px solid rgba(224, 224, 224, 0.5)',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    // First column (symbol) gets both top and left sticky positioning
                    ...(colIndex === 0
                      ? {
                          left: 0,
                          zIndex: 10, // Higher z-index for corner cell
                          borderRight: `2px solid rgba(224, 224, 224, ${isScrolled ? '0.8' : '0.5'})`,
                          boxShadow: isScrolled
                            ? '4px 0 8px rgba(0, 0, 0, 0.15)'
                            : '2px 0 4px rgba(0, 0, 0, 0.1)',
                          transition:
                            'box-shadow 0.2s ease-in-out, border-right 0.2s ease-in-out',
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            right: '-1px',
                            bottom: 0,
                            width: '1px',
                            background: isScrolled
                              ? 'rgba(25, 118, 210, 0.3)'
                              : 'transparent',
                            pointerEvents: 'none',
                            transition: 'background 0.2s ease-in-out',
                          },
                        }
                      : {
                          zIndex: 3, // Regular header cells
                        }),
                  }}
                >
                  {getColumnLabel(column)}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {enhancedCandidates.map((candidate, index) => {
              const isSelected = selectedRowIndex === index;
              return (
                <TableRow
                  key={index}
                  onClick={() => setSelectedRowIndex(isSelected ? null : index)}
                  sx={{
                    position: 'relative',
                    cursor: 'pointer',
                    // Row selection and hover states
                    backgroundColor: isSelected
                      ? 'rgba(25, 118, 210, 0.08)'
                      : 'transparent',
                    '&:hover': {
                      backgroundColor: isSelected
                        ? 'rgba(25, 118, 210, 0.12)'
                        : 'rgba(0, 0, 0, 0.04)',
                    },
                    // Row separator
                    '&:not(:last-child)': {
                      borderBottom: '1px solid rgba(224, 224, 224, 0.2)',
                    },
                    // Smooth transitions
                    transition: 'background-color 0.2s ease-in-out',
                  }}
                >
                  {columns.map((column, colIndex) => (
                    <TableCell
                      key={column}
                      sx={{
                        // First column (symbol) sticky positioning
                        ...(colIndex === 0
                          ? {
                              position: 'sticky',
                              left: 0,
                              zIndex: 2,
                              minWidth: '160px',
                              maxWidth: '160px',
                              width: '160px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              // Dynamic background that inherits row state
                              backgroundColor: isSelected
                                ? 'rgba(25, 118, 210, 0.08)'
                                : '#ffffff',
                              borderRight: `2px solid rgba(224, 224, 224, ${isScrolled ? '0.8' : '0.5'})`,
                              boxShadow: isScrolled
                                ? '4px 0 8px rgba(0, 0, 0, 0.15)'
                                : '2px 0 4px rgba(0, 0, 0, 0.1)',
                              transition: 'all 0.2s ease-in-out',
                              // Visual indicator for sticky boundary
                              '&::after': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                right: '-1px',
                                bottom: 0,
                                width: '1px',
                                background: isScrolled
                                  ? 'rgba(25, 118, 210, 0.3)'
                                  : 'transparent',
                                pointerEvents: 'none',
                                transition: 'background 0.2s ease-in-out',
                              },
                            }
                          : {
                              // Regular cells inherit background
                              backgroundColor: 'inherit',
                            }),
                      }}
                    >
                      {colIndex === 0
                        ? formatCellValue(
                            candidate.symbol || candidate[column],
                            'symbol',
                            candidate
                          )
                        : formatCellValue(candidate[column], column, candidate)}
                    </TableCell>
                  ))}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  const getColumnLabel = column => {
    const labels = {
      // Core columns with English/Chinese labels
      symbol: 'Symbol (股票代码)',
      currentPrice: 'Cur. Price (当前价格)',
      strike: 'Strike (行权价)',
      expiration: 'Expires (到期日)',
      dte: 'DTE (剩余天数)',
      bid: 'Bid (买价)',
      premium: 'Premium (权利金)',
      annualizedRoi: 'Ann. ROI (年化收益率)',
      bufferPercent: 'Buffer % (缓冲比例)',
      upsideBufferPercent: 'Upside % (上涨空间)',
      totalReturn: 'Total Ret. (总收益)',
      winRateScore: 'Win Score (胜率评分)',
      impliedVolatility: 'IV (隐含波动率)',
      roiPercent: 'ROI % (投资回报率)',
      opportunityCost: 'Opp. Cost (机会成本)',
      maxGain: 'Max Gain (最大收益)',

      // Iron condors specific columns
      netPremium: 'Net Premium (净权利金)',
      maxProfit: 'Max Profit (最大收益)',
      maxLoss: 'Max Loss (最大损失)',
      probabilityOfProfit: 'Prob. Profit (盈利概率)',
    };
    return labels[column] || column;
  };

  const getColumnMinWidth = column => {
    const widths = {
      symbol: 160,
      currentPrice: 110,
      strike: 100,
      expiration: 120,
      dte: 80,
      bid: 90,
      premium: 100,
      annualizedRoi: 120,
      bufferPercent: 110,
      upsideBufferPercent: 120,
      totalReturn: 110,
      winRateScore: 140,
      impliedVolatility: 100,
      roiPercent: 100,
      opportunityCost: 130,
      maxGain: 110,
    };
    return widths[column] || 100;
  };

  const getColumnMaxWidth = column => {
    const widths = {
      symbol: 200,
      winRateScore: 180,
      opportunityCost: 160,
      annualizedRoi: 150,
      upsideBufferPercent: 150,
      expiration: 140,
    };
    return widths[column] || 140;
  };

  const formatCellValue = (value, column) => {
    if (value === null || value === undefined) {
      return <span style={{ color: '#999', fontStyle: 'italic' }}>N/A</span>;
    }

    // Symbol with clean, professional styling and strict containment
    if (column === 'symbol') {
      return (
        <Typography
          variant="body2"
          sx={{
            fontWeight: '700',
            fontSize: '0.9rem',
            color: '#1976D2',
            fontFamily: 'monospace',
            letterSpacing: '0.5px',
            // Ensure symbol text doesn't overflow
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '140px', // Leave some padding within the 160px cell
            display: 'block',
          }}
        >
          {value}
        </Typography>
      );
    }

    // Currency values with enhanced styling
    if (
      [
        'currentPrice',
        'strike',
        'bid',
        'premium',
        'totalReturn',
        'opportunityCost',
        'maxGain',
        'netPremium',
        'maxProfit',
        'maxLoss',
      ].includes(column)
    ) {
      const amount = Number(value);
      const isHighValue = amount > 100;
      const isProfit =
        ['totalReturn', 'maxGain', 'maxProfit'].includes(column) && amount > 0;
      const isLoss = ['maxLoss'].includes(column) && amount < 0;

      return (
        <Typography
          variant="body2"
          sx={{
            fontWeight: isHighValue ? '700' : '600',
            color: isProfit
              ? '#2E7D32'
              : isLoss
                ? '#C62828'
                : isHighValue
                  ? '#1976D2'
                  : '#64748B',
            fontFamily: 'monospace',
            fontSize: '0.85rem',
            background: isProfit
              ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(46, 125, 50, 0.05) 100%)'
              : isLoss
                ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.08) 0%, rgba(198, 40, 40, 0.05) 100%)'
                : 'transparent',
            borderRadius: '4px',
            px: 0.5,
            py: 0.25,
          }}
        >
          ${amount.toFixed(2)}
        </Typography>
      );
    }

    // Percentage values with color coding
    if (
      [
        'annualizedRoi',
        'bufferPercent',
        'upsideBufferPercent',
        'impliedVolatility',
        'roiPercent',
        'probabilityOfProfit',
      ].includes(column)
    ) {
      const percentage = Number(value) * 100;
      const isGood = percentage > 5;
      const isModerate = percentage > 2;

      return (
        <Typography
          variant="body2"
          sx={{
            fontWeight: '700',
            color: isGood ? '#2E7D32' : isModerate ? '#F57C00' : '#C62828',
            fontFamily: 'monospace',
            fontSize: '0.85rem',
            background: isGood
              ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(46, 125, 50, 0.05) 100%)'
              : isModerate
                ? 'linear-gradient(135deg, rgba(255, 152, 0, 0.08) 0%, rgba(245, 124, 0, 0.05) 100%)'
                : 'linear-gradient(135deg, rgba(244, 67, 54, 0.08) 0%, rgba(198, 40, 40, 0.05) 100%)',
            borderRadius: '4px',
            px: 0.5,
            py: 0.25,
          }}
        >
          {percentage.toFixed(1)}%
        </Typography>
      );
    }

    // Integer values (days) with urgency coloring
    if (column === 'dte') {
      const days = Number(value);
      const isUrgent = days < 30;
      const isModerate = days < 45;

      return (
        <Typography
          variant="body2"
          sx={{
            fontWeight: '700',
            color: isUrgent ? '#C62828' : isModerate ? '#F57C00' : '#2E7D32',
            fontSize: '0.85rem',
            background: isUrgent
              ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.08) 0%, rgba(198, 40, 40, 0.05) 100%)'
              : isModerate
                ? 'linear-gradient(135deg, rgba(255, 152, 0, 0.08) 0%, rgba(245, 124, 0, 0.05) 100%)'
                : 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(46, 125, 50, 0.05) 100%)',
            borderRadius: '4px',
            px: 0.5,
            py: 0.25,
            minWidth: '30px',
            textAlign: 'center',
          }}
        >
          {days}
        </Typography>
      );
    }

    // Win rate score with progress bar styling
    if (column === 'winRateScore') {
      const score = Number(value);
      const percentage = Math.min(score, 100);
      const color = score > 60 ? '#4caf50' : score > 40 ? '#ff9800' : '#f44336';

      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <div
            style={{
              width: '40px',
              height: '8px',
              backgroundColor: '#e0e0e0',
              borderRadius: '4px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                width: `${percentage}%`,
                height: '100%',
                backgroundColor: color,
                transition: 'width 0.3s ease',
              }}
            />
          </div>
          <span
            style={{
              fontSize: '0.7rem',
              fontWeight: 'bold',
              color: color,
              minWidth: '25px',
            }}
          >
            {score.toFixed(1)}
          </span>
        </div>
      );
    }

    // Date formatting with better styling
    if (column === 'expiration') {
      const date = new Date(value);
      const isNear = date - new Date() < 30 * 24 * 60 * 60 * 1000; // 30 days

      return (
        <Typography
          variant="body2"
          sx={{
            fontFamily: 'monospace',
            fontSize: '0.8rem',
            color: isNear ? '#C62828' : '#64748B',
            fontWeight: isNear ? '700' : '600',
            background: isNear
              ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.08) 0%, rgba(198, 40, 40, 0.05) 100%)'
              : 'transparent',
            borderRadius: '4px',
            px: 0.5,
            py: 0.25,
          }}
        >
          {date.toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric',
          })}
        </Typography>
      );
    }

    // Default: return as-is for any unhandled columns
    return (
      <Typography
        variant="body2"
        sx={{
          fontSize: '0.85rem',
          color: '#64748B',
          fontWeight: '500',
        }}
      >
        {value}
      </Typography>
    );
  };

  return (
    <Box>
      {renderStrategySelector()}
      {renderWatchlistSelector()}
      {renderAnalysisControls()}
      {renderAnalysisResults()}

      {/* Configuration Dialog */}
      <StrategyConfigDialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        selectedAccount={selectedAccount}
        strategyType={selectedStrategy}
        currentConfig={strategyConfig}
        onConfigSaved={newConfig => {
          setStrategyConfig(newConfig);
          toast.success('配置已更新');
        }}
      />

      {/* Watchlist Management Dialog */}
      <WatchlistDialog
        open={watchlistDialogOpen}
        onClose={() => setWatchlistDialogOpen(false)}
        selectedAccount={selectedAccount}
        onWatchlistsUpdated={loadWatchlists}
      />
    </Box>
  );
};

export default OptionsAnalysisPanel;
