import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Box,
  Tooltip,
  IconButton,
  Collapse,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  Info as InfoIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from '@mui/icons-material';
import AddAccountDialog from '../forms/AddAccountDialog';
import ImportTransactionsDialog from '../forms/ImportTransactionsDialog';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';
import { get, post, del } from '../../services/api';
import toast from 'react-hot-toast';
import {
  <PERSON><PERSON>ard,
  PrimaryButton,
  SecondaryButton,
  DestructiveButton,
  ModernTableHeadCell,
  ValueCell,
  LoadingBox,
  StyledAlert,
  GradientTypography,
  CollapseContent,
} from '../../styles/common';

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:nth-of-type(odd)': {
    background:
      'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,250,252,0.9) 100%)',
  },
  '&:nth-of-type(even)': {
    background:
      'linear-gradient(135deg, rgba(248,250,252,0.6) 0%, rgba(255,255,255,0.8) 100%)',
  },
  '&:hover': {
    background:
      'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%) !important',
    transform: 'scale(1.002)',
  },
  '& td': {
    borderBottom: '1px solid rgba(224, 224, 224, 0.3)',
  },
  cursor: 'pointer',
}));

const ActionButton = ({ deleteButton, ...props }) => {
  if (deleteButton) {
    return <DestructiveButton {...props} />;
  }
  return <SecondaryButton {...props} />;
};

const HeaderCell = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
}));

const DetailRow = styled(TableRow)(({ theme }) => ({
  backgroundColor: 'transparent',
  // 优化性能：减少复杂的样式计算
  '& > td': {
    paddingBottom: 0,
    paddingTop: 0,
    paddingLeft: '20px',
    paddingRight: '20px',
    border: 'none',
    // 使用简单的背景色替代复杂渐变，提升性能
    backgroundColor: 'rgba(248,250,252,0.5)',
  },
  // 移除过渡效果以减少重绘
}));

const DetailCell = styled(TableCell)(({ theme }) => ({
  // 确保与主表格单元格对齐
  padding: '0 20px 20px 20px',
  backgroundColor: 'transparent',
  border: 'none',
  // 添加与主表格一致的边框和间距
  borderBottom: '1px solid rgba(224, 224, 224, 0.1)',
  // 确保内容区域有足够的内边距
  '& > div': {
    padding: '16px 0',
  },
}));

// 共享的表格容器样式
const sharedTableContainerStyles = {
  borderRadius: '8px',
  // 确保容器背景完全不透明
  backgroundColor: '#ffffff',
  border: '1px solid rgba(25, 118, 210, 0.1)',
  overflow: 'auto',
  '&::-webkit-scrollbar': {
    width: '8px',
    height: '8px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: '#f5f5f5',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#bdbdbd',
    borderRadius: '4px',
    '&:hover': {
      backgroundColor: '#9e9e9e',
    },
  },
  '& .MuiTable-root': {
    minWidth: '100%',
    tableLayout: 'auto',
  },
};

// 共享的内容区域样式
const sharedContentStyles = {
  backgroundColor: 'rgba(248,250,252,0.4)',
  border: '1px solid rgba(25, 118, 210, 0.08)',
  borderRadius: '8px',
};

// 共享的表格单元格样式
const sharedTableCellStyles = {
  fontWeight: '500',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
};

// 共享的表格行样式
const sharedTableRowStyles = {
  '&:hover': {
    backgroundColor: 'rgba(25, 118, 210, 0.04)',
  },
  '&:nth-of-type(even)': {
    backgroundColor: 'rgba(248, 250, 252, 0.3)',
  },
};

// 共享的 sticky header 样式
const stickyHeaderStyles = {
  position: 'sticky',
  top: 0,
  // 使用不透明的背景确保完全遮挡下面的内容
  backgroundColor: '#ffffff',
  // 添加渐变背景以保持视觉效果
  backgroundImage:
    'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%)',
  zIndex: 11,
  borderBottom: '2px solid rgba(25, 118, 210, 0.1)',
  fontWeight: '700',
  color: '#1976D2',
  // 添加阴影以增强分离效果
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
};

// 自定义 Hook：管理股票详情
const useStockDetails = (accountId, open) => {
  const [stockDetails, setStockDetails] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchStockDetails = useCallback(async () => {
    if (!open) return;

    try {
      setLoading(true);
      const data = await get(`/api/analytics/holdings?account_id=${accountId}`);
      setStockDetails(data.holdings || []);
    } catch (error) {
      console.error('Error fetching stock details:', error);
    } finally {
      setLoading(false);
    }
  }, [accountId, open]);

  useEffect(() => {
    fetchStockDetails();
  }, [fetchStockDetails]);

  return { stockDetails, loading };
};

const DetailTableContainer = styled(TableContainer)(({ theme }) => ({
  ...sharedTableContainerStyles,
  // 固定最大高度以确保滚动功能
  maxHeight: '400px',
  // 确保滚动功能正常工作
  overflowY: 'auto',
  overflowX: 'auto',
  // 优化滚动性能
  scrollBehavior: 'smooth',
  // 确保 sticky header 在滚动时正常工作
  position: 'relative',
  // 响应式高度调整
  [theme.breakpoints.up('sm')]: {
    maxHeight: '500px',
  },
  [theme.breakpoints.up('md')]: {
    maxHeight: '600px',
  },
  [theme.breakpoints.up('lg')]: {
    maxHeight: '700px',
  },
  [theme.breakpoints.up('xl')]: {
    maxHeight: '800px',
  },
}));

const StockDetailTable = styled(Table)(({ theme }) => ({
  // 使用不透明背景确保表格内容不透明
  backgroundColor: '#ffffff',
  // Ensure table takes full width and expands properly
  width: '100%',
  tableLayout: 'fixed', // 使用固定布局以更好地控制列宽
  minWidth: '800px', // 设置固定的最小宽度
  // 移除 '& th' 样式，让表头使用共享的 stickyHeaderStyles
  '& td': {
    // Responsive font sizes for table cells
    fontSize: {
      xs: '0.75rem',
      sm: '0.8rem',
      md: '0.875rem',
      lg: '0.9rem',
    },
    // Responsive padding
    padding: {
      xs: '8px 12px',
      sm: '10px 14px',
      md: '12px 16px',
    },
    // Prevent text wrapping for better layout
    whiteSpace: 'nowrap',
    // Ensure proper text overflow handling
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  '& tbody tr': {
    // 移除过渡效果以提升性能
    '&:hover': {
      backgroundColor: 'rgba(25, 118, 210, 0.04)',
    },
    '&:nth-of-type(even)': {
      backgroundColor: 'rgba(248, 250, 252, 0.3)',
    },
  },
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  ...sharedTableContainerStyles,
  borderRadius: '12px',
  boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
}));

const Row = React.memo(function Row({
  account,
  onDelete,
  onAddStock,
  onImport,
}) {
  const [open, setOpen] = useState(false);
  const accountId = account?.id || account?.account_id; // 兼容两种 ID 字段名

  // 使用自定义 Hook 管理股票详情
  const { stockDetails, loading } = useStockDetails(accountId, open);

  // 使用 useMemo 优化股票详情渲染性能
  const memoizedStockDetails = useMemo(() => {
    return stockDetails.map((stock, index) => (
      <TableRow key={`${accountId}-${stock.symbol}`} sx={sharedTableRowStyles}>
        <TableCell
          component="th"
          scope="row"
          sx={{
            ...sharedTableCellStyles,
            fontWeight: '600',
            color: '#1976D2',
            minWidth: '100px',
            maxWidth: '150px',
          }}
        >
          {stock.symbol}
        </TableCell>
        <TableCell
          align="right"
          sx={{
            ...sharedTableCellStyles,
            minWidth: '100px',
          }}
        >
          {(stock.quantity || 0).toLocaleString()}
        </TableCell>
        <TableCell
          align="right"
          sx={{
            ...sharedTableCellStyles,
            minWidth: '120px',
          }}
        >
          $
          {(stock.cost_per_share || 0).toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </TableCell>
        <TableCell
          align="right"
          sx={{
            ...sharedTableCellStyles,
            minWidth: '120px',
          }}
        >
          $
          {(stock.current_price || 0).toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </TableCell>
        <TableCell
          align="right"
          sx={{
            ...sharedTableCellStyles,
            minWidth: '120px',
          }}
        >
          $
          {(stock.cost || 0).toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </TableCell>
        <TableCell
          align="right"
          sx={{
            ...sharedTableCellStyles,
            minWidth: '120px',
          }}
        >
          $
          {(stock.value || 0).toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </TableCell>
        <ValueCell
          align="right"
          value={stock.gains}
          sx={{
            ...sharedTableCellStyles,
            minWidth: '120px',
          }}
        >
          $
          {(stock.gains || 0).toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </ValueCell>
        <ValueCell
          align="right"
          value={stock.return_rate}
          sx={{
            ...sharedTableCellStyles,
            minWidth: '100px',
          }}
        >
          {(stock.return_rate || 0) > 0 ? '+' : ''}
          {((stock.return_rate || 0) * 100).toFixed(2)}%
        </ValueCell>
      </TableRow>
    ));
  }, [stockDetails, accountId]);

  return (
    <>
      <StyledTableRow onClick={() => setOpen(!open)}>
        <TableCell sx={{ padding: '16px 20px' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton
              size="small"
              sx={{
                color: '#1976D2',
                transition: 'all 0.2s ease',
                '&:hover': {
                  background: 'rgba(25, 118, 210, 0.1)',
                  transform: 'scale(1.1)',
                },
              }}
            >
              {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </IconButton>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: '700',
                fontSize: '1.1rem',
                background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              {account.name}
            </Typography>
          </Box>
        </TableCell>
        <ValueCell
          align="right"
          sx={{ padding: '16px 20px', fontWeight: '600' }}
        >
          ${(account.total_cost || 0).toLocaleString()}
        </ValueCell>
        <ValueCell
          align="right"
          sx={{ padding: '16px 20px', fontWeight: '600' }}
        >
          ${(account.current_value || 0).toLocaleString()}
        </ValueCell>
        <ValueCell
          align="right"
          value={account.realized_gains}
          sx={{ padding: '16px 20px' }}
        >
          ${(account.realized_gains || 0).toLocaleString()}
        </ValueCell>
        <ValueCell
          align="right"
          value={account.total_gains}
          sx={{ padding: '16px 20px' }}
        >
          ${(account.total_gains || 0).toLocaleString()}
        </ValueCell>
        <ValueCell
          align="right"
          value={account.return_rate}
          sx={{ padding: '16px 20px' }}
        >
          {(account.return_rate || 0) > 0 ? '+' : ''}
          {((account.return_rate || 0) * 100).toFixed(2)}%
        </ValueCell>
        <TableCell
          align="right"
          onClick={e => e.stopPropagation()}
          sx={{ padding: '12px 20px' }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              gap: 1.5,
            }}
          >
            <ActionButton
              size="small"
              variant="outlined"
              onClick={() => onImport(accountId)}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <UploadIcon fontSize="small" />
                <span>导入交易</span>
              </Box>
            </ActionButton>
            <ActionButton
              size="small"
              variant="outlined"
              deleteButton={true}
              onClick={e => {
                e.stopPropagation();
                if (accountId) {
                  onDelete(accountId, e);
                } else {
                  console.error('Account ID is missing:', account);
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <DeleteIcon fontSize="small" />
                <span>删除</span>
              </Box>
            </ActionButton>
          </Box>
        </TableCell>
      </StyledTableRow>
      <DetailRow key={`detail-${accountId}`}>
        <DetailCell colSpan={7}>
          <Collapse
            in={open}
            timeout={200}
            unmountOnExit
            easing={{
              enter: 'cubic-bezier(0.4, 0, 0.2, 1)',
              exit: 'cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            <CollapseContent
              sx={{
                // 确保与主表格完美对齐
                margin: '0 -20px',
                padding: '20px',
                // 使用共享的内容样式
                ...sharedContentStyles,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 2,
                  pb: 1,
                  borderBottom: '2px solid rgba(25, 118, 210, 0.1)',
                }}
              >
                <Typography
                  variant="h6"
                  component="div"
                  sx={{
                    fontWeight: '700',
                    color: '#1976D2',
                    fontSize: '1.1rem',
                  }}
                >
                  持仓明细
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    px: 2,
                    py: 0.5,
                    borderRadius: '12px',
                    background:
                      'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
                    color: '#1976D2',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                  }}
                >
                  {stockDetails.length} 只股票
                </Box>
              </Box>
              {loading ? (
                <LoadingBox>
                  <CircularProgress sx={{ color: '#1976D2' }} />
                </LoadingBox>
              ) : (
                <Box
                  sx={{
                    width: '100%',
                    // 确保与主表格对齐
                    mx: 0,
                    // 添加与主表格一致的边距
                    mt: 2,
                  }}
                >
                  <DetailTableContainer
                    sx={{
                      // 强制设置最大高度以确保滚动
                      maxHeight: '400px !important',
                      overflowY: 'auto !important',
                      overflowX: 'auto !important',
                      border: '1px solid rgba(25, 118, 210, 0.1)',
                    }}
                  >
                    <StockDetailTable size="small" stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '100px',
                            }}
                          >
                            股票代码
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '100px',
                            }}
                          >
                            持仓数量
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '120px',
                            }}
                          >
                            每股成本 (USD)
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '120px',
                            }}
                          >
                            当前价格 (USD)
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '120px',
                            }}
                          >
                            总成本 (USD)
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '120px',
                            }}
                          >
                            当前市值 (USD)
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '120px',
                            }}
                          >
                            收益 (USD)
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              ...stickyHeaderStyles,
                              minWidth: '100px',
                            }}
                          >
                            收益率
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>{memoizedStockDetails}</TableBody>
                    </StockDetailTable>
                  </DetailTableContainer>
                </Box>
              )}
            </CollapseContent>
          </Collapse>
        </DetailCell>
      </DetailRow>
    </>
  );
});

function AllAccountSummary({ onRefresh }) {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openAddAccount, setOpenAddAccount] = useState(false);
  const [openImport, setOpenImport] = useState(false);
  const [selectedAccountId, setSelectedAccountId] = useState(null);
  const [refreshCounter, setRefreshCounter] = useState(0);

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await get(API_ENDPOINTS.OVERVIEW);
      setAccounts(data.account_breakdown || []);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      setError('获取账户数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('触发账户数据刷新...');
    fetchAccounts();
  }, [refreshCounter]);

  const handleDelete = async (accountId, event) => {
    // 确保事件处理
    if (event) {
      event.stopPropagation();
    }

    // 验证 accountId
    if (!accountId) {
      const error = '无效的账户ID';
      console.error(error, { accountId });
      setError(error);
      return;
    }

    console.log('Attempting to delete account:', accountId);

    if (!window.confirm('确定要删除这个账户吗？')) {
      console.log('Delete cancelled by user');
      return;
    }

    try {
      console.log('Sending delete request for account:', accountId);
      await del(`${API_ENDPOINTS.ACCOUNTS}/${accountId}`);

      console.log('Successfully deleted account:', accountId);
      toast.success('账户删除成功');
      // 使用 id 或 account_id 进行过滤
      setAccounts(prev =>
        prev.filter(acc => (acc.id || acc.account_id) !== accountId)
      );

      if (onRefresh) {
        console.log('Triggering refresh after delete');
        onRefresh();
      }
    } catch (error) {
      const errorMessage = '删除账户失败';
      console.error(errorMessage, { accountId, error });
      toast.error(`${errorMessage}: ${error.message}`);
      setError(`${errorMessage}: ${error.message}`);
    }
  };

  const handleCreateAccount = async accountData => {
    try {
      await post(API_ENDPOINTS.ACCOUNTS, accountData);
      toast.success('账户创建成功');
      setOpenAddAccount(false);
      fetchAccounts();
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error creating account:', error);
      toast.error(`创建账户失败: ${error.message}`);
      throw error;
    }
  };

  return (
    <>
      <PageCard>
        <CardContent sx={{ p: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <GradientTypography>账户汇总</GradientTypography>
            <Box>
              <PrimaryButton
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenAddAccount(true)}
              >
                新增账户
              </PrimaryButton>
            </Box>
          </Box>
          {loading ? (
            <LoadingBox>
              <CircularProgress sx={{ color: '#1976D2' }} />
            </LoadingBox>
          ) : error ? (
            <StyledAlert severity="error">{error}</StyledAlert>
          ) : (
            <StyledTableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <ModernTableHeadCell>
                      <HeaderCell>
                        账户名称
                        <Tooltip title="账户的唯一标识名称" arrow>
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        总成本 (USD)
                        <Tooltip
                          title="所有持仓股票的总成本基础 = Σ(每支股票的持仓数量 × 平均成本价格)"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        当前市值 (USD)
                        <Tooltip
                          title="所有持仓股票的当前市值总和 = Σ(每支股票的持仓数量 × 当前价格)"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        已实现收益 (USD)
                        <Tooltip
                          title="已经卖出股票的收益总和 = Σ(卖出价格 - 买入价格) × 卖出数量"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        总收益 (USD)
                        <Tooltip
                          title="当前持仓的未实现总收益 + 历史已实现总收益"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        持仓收益率
                        <Tooltip
                          title="当前持仓的未实现总收益 / 当前持仓的总成本 × 100%"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      操作
                    </ModernTableHeadCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {accounts.map(account => (
                    <Row
                      key={account.account_id}
                      account={account}
                      onDelete={handleDelete}
                      onImport={accountId => {
                        setSelectedAccountId(accountId);
                        setOpenImport(true);
                      }}
                    />
                  ))}
                </TableBody>
              </Table>
            </StyledTableContainer>
          )}
        </CardContent>
      </PageCard>

      <AddAccountDialog
        open={openAddAccount}
        onClose={() => setOpenAddAccount(false)}
        onSubmit={handleCreateAccount}
      />

      <ImportTransactionsDialog
        open={openImport}
        onClose={() => {
          setOpenImport(false);
          setSelectedAccountId(null);
        }}
        onSuccess={async importedCount => {
          const message = `导入完成: 共导入 ${importedCount} 条记录`;
          console.log(message);
          toast.success(message);
          setRefreshCounter(prev => prev + 1);
          await fetchAccounts();
          if (onRefresh) {
            console.log('调用父组件刷新方法...');
            onRefresh();
          }
          console.log('所有刷新完成');
        }}
        accountId={selectedAccountId}
      />
    </>
  );
}

export default AllAccountSummary;
