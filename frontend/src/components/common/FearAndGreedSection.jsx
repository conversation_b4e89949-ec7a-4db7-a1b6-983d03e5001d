import { useState } from 'react';
import {
  Card<PERSON>ontent,
  Typography,
  Box,
  CircularProgress,
  CardHeader,
  Tooltip,
  IconButton,
  useTheme,
  Paper,
  useMediaQuery,
  Chip,
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

// 动画关键帧定义
const pulse = keyframes`
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
`;

const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
`;
import Launch from '@mui/icons-material/Launch';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import RemoveIcon from '@mui/icons-material/Remove';
import SpeedIcon from '@mui/icons-material/Speed';
import { GlassCard, GradientTypography } from '../../styles/common';

// 简洁专业的恐惧贪婪指数卡片
const FearGreedCard = styled(GlassCard)(({ rangeInfo }) => ({
  background: 'rgba(255,255,255,0.95)',
  border: `2px solid ${rangeInfo?.color || '#1976D2'}30`,
  borderRadius: '12px',
  boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
  transition: 'all 0.2s ease',
  '&:hover': {
    boxShadow: '0 6px 20px rgba(0,0,0,0.12)',
  },
}));

// 简洁状态面板
const StatusPanel = styled(Paper)(({ rangeInfo }) => ({
  padding: '16px 24px',
  background: 'rgba(255,255,255,0.9)',
  border: `1px solid ${rangeInfo?.color || '#1976D2'}20`,
  borderRadius: '8px',
  display: 'flex',
  alignItems: 'center',
  gap: '16px',
  transition: 'all 0.2s ease',
}));

// 简洁时间戳
const TimestampChip = styled(Paper)(({ theme }) => ({
  padding: '8px 16px',
  background: 'rgba(248,250,252,0.9)',
  borderRadius: '20px',
  border: '1px solid rgba(0,0,0,0.08)',
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
}));

// 美化的卡片标题
const StyledCardHeader = styled(CardHeader)(({ theme }) => ({
  paddingBottom: '16px',
  paddingTop: '24px',
  paddingLeft: '24px',
  paddingRight: '24px',
  '& .MuiCardHeader-title': {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
}));

// 专业级市场情绪分级
const GAUGE_RANGES = [
  {
    limit: 25,
    color: '#dc2626',
    bgColor: '#fef2f2',
    label: 'Extreme Fear',
    emoji: '📉',
    zhLabel: '极度恐慌',
    description: '市场极度恐慌，可能是买入机会',
    riskLevel: '高风险高回报',
    advice: '谨慎抄底',
  },
  {
    limit: 45,
    color: '#ea580c',
    bgColor: '#fff7ed',
    label: 'Fear',
    emoji: '📊',
    zhLabel: '恐慌',
    description: '市场情绪偏谨慎，估值相对合理',
    riskLevel: '中等风险',
    advice: '分批布局',
  },
  {
    limit: 55,
    color: '#6b7280',
    bgColor: '#f9fafb',
    label: 'Neutral',
    emoji: '⚖️',
    zhLabel: '中性',
    description: '市场情绪平衡，等待方向选择',
    riskLevel: '低风险',
    advice: '观望为主',
  },
  {
    limit: 75,
    color: '#16a34a',
    bgColor: '#f0fdf4',
    label: 'Greed',
    emoji: '📈',
    zhLabel: '贪婪',
    description: '市场情绪乐观，注意获利了结',
    riskLevel: '中等风险',
    advice: '适度减仓',
  },
  {
    limit: 100,
    color: '#2563eb',
    bgColor: '#eff6ff',
    label: 'Extreme Greed',
    emoji: '🚀',
    zhLabel: '极度贪婪',
    description: '市场过热，泡沫风险增加',
    riskLevel: '高风险',
    advice: '获利了结',
  },
];

const getRangeInfo = value => {
  if (value === null || value === undefined) {
    const neutralRange = GAUGE_RANGES[2];
    return { ...neutralRange, label: 'N/A', zhLabel: '未知', emoji: '❓' };
  }
  for (const range of GAUGE_RANGES) {
    if (value <= range.limit) {
      return range;
    }
  }
  return GAUGE_RANGES[GAUGE_RANGES.length - 1];
};

const formatTimestamp = ts =>
  ts ? new Date(ts).toLocaleString('zh-CN') : 'N/A';

// 简洁专业的市场情绪仪表盘
const ProfessionalGauge = ({ value, size, timestamp, fearGreedData }) => {
  const theme = useTheme();
  const rangeInfo = getRangeInfo(value);
  const percentage = value / 100;

  const radius = size * 0.4;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - percentage * circumference;
  const strokeWidth = Math.max(size * 0.06, 12);

  let TrendIcon = RemoveIcon;
  if (value <= 44) {
    TrendIcon = TrendingDownIcon;
  } else if (value >= 56) {
    TrendIcon = TrendingUpIcon;
  }

  const formatTimeAgo = timestamp => {
    if (!timestamp) return '未知';
    const now = new Date();
    const updateTime = new Date(timestamp);
    const diffMs = now - updateTime;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return '刚刚更新';
    if (diffMins < 60) return `${diffMins}分钟前`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}小时前`;
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
      }}
    >
      {/* 主仪表盘 */}
      <Box
        sx={{
          position: 'relative',
          display: 'inline-flex',
          width: size,
          height: size,
        }}
      >
        <svg width={size} height={size} style={{ transform: 'rotate(-90deg)' }}>
          {/* 背景圆环 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={theme.palette.grey[300]}
            strokeWidth={strokeWidth}
            fill="transparent"
          />

          {/* 进度圆环 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={rangeInfo.color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            style={{
              transition: 'stroke-dashoffset 0.8s ease-in-out',
            }}
          />
        </svg>

        {/* 中心内容 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
          }}
        >
          <Typography
            variant="h2"
            component="div"
            sx={{
              fontWeight: 600,
              color: rangeInfo.color,
              fontSize: `${size * 0.12}px`,
              lineHeight: 1,
            }}
          >
            {value}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: theme.palette.text.secondary,
              fontSize: `${size * 0.035}px`,
              mt: 0.5,
            }}
          >
            / 100
          </Typography>
        </Box>
      </Box>

      {/* 状态信息面板 */}
      <Box
        sx={{
          mt: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          width: '100%',
          maxWidth: 300,
        }}
      >
        {/* 主要状态 */}
        <StatusPanel rangeInfo={rangeInfo}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              width: '100%',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                minWidth: 50,
              }}
            >
              <TrendIcon
                sx={{
                  fontSize: 32,
                  color: rangeInfo.color,
                  mb: 0.5,
                }}
              />
              <Typography variant="h4" sx={{ fontSize: '1.5rem' }}>
                {rangeInfo.emoji}
              </Typography>
            </Box>

            <Box sx={{ flex: 1 }}>
              <Typography
                variant="h6"
                fontWeight={600}
                sx={{
                  color: rangeInfo.color,
                  mb: 0.5,
                }}
              >
                {rangeInfo.zhLabel}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.text.secondary,
                  fontSize: '0.875rem',
                  mb: 0.5,
                }}
              >
                {rangeInfo.label}
              </Typography>
              <Chip
                label={rangeInfo.riskLevel}
                size="small"
                sx={{
                  backgroundColor: `${rangeInfo.color}10`,
                  color: rangeInfo.color,
                  fontSize: '0.75rem',
                }}
              />
            </Box>
          </Box>
        </StatusPanel>

        {/* 详细分析 */}
        <Paper
          elevation={1}
          sx={{
            p: 2,
            borderRadius: '8px',
            backgroundColor: 'background.paper',
            border: `1px solid ${rangeInfo.color}15`,
            width: '100%',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              lineHeight: 1.5,
              mb: 1,
            }}
          >
            {rangeInfo.description}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: rangeInfo.color,
              fontWeight: 500,
            }}
          >
            建议: {rangeInfo.advice}
          </Typography>
        </Paper>

        {/* 更新时间 */}
        <TimestampChip>
          <SpeedIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              fontSize: '0.75rem',
            }}
          >
            {formatTimeAgo(timestamp)}
          </Typography>
        </TimestampChip>
      </Box>
    </Box>
  );
};

const FearAndGreedSection = ({
  fearGreedData,
  fearGreedLoading,
  fearGreedError,
  sx,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const [showDetails, setShowDetails] = useState(false);

  // 响应式尺寸计算
  const gaugeSize = isMobile ? 200 : isTablet ? 240 : 280;
  const isCompactLayout = isMobile || isTablet;

  // 加载动画
  if (fearGreedLoading) {
    return (
      <FearGreedCard rangeInfo={{ color: '#1976D2' }} sx={sx}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: { xs: 300, sm: 400, md: 450 },
            p: 4,
          }}
        >
          <CircularProgress
            size={isMobile ? 48 : 60}
            sx={{
              color: theme.palette.primary.main,
              mb: 3,
              animation: `${pulse} 2s ease-in-out infinite`,
            }}
          />
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{
              fontWeight: 600,
              textAlign: 'center',
              animation: `${float} 3s ease-in-out infinite`,
            }}
          >
            正在分析市场情绪...
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              textAlign: 'center',
              mt: 1,
              opacity: 0.7,
            }}
          >
            获取最新恐惧贪婪指数数据
          </Typography>
        </Box>
      </FearGreedCard>
    );
  }

  if (fearGreedError) {
    return (
      <FearGreedCard rangeInfo={{ color: '#d32f2f' }} sx={sx}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 300,
            p: 4,
          }}
        >
          <Typography
            variant="h6"
            color="error"
            sx={{ fontWeight: 600, textAlign: 'center', mb: 2 }}
          >
            数据获取失败
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ textAlign: 'center' }}
          >
            {fearGreedError}
          </Typography>
        </Box>
      </FearGreedCard>
    );
  }

  if (
    !fearGreedData ||
    fearGreedData.value === null ||
    fearGreedData.value === undefined
  ) {
    return (
      <FearGreedCard rangeInfo={{ color: '#f57c00' }} sx={sx}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 300,
            p: 4,
          }}
        >
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{ fontWeight: 600, textAlign: 'center', mb: 2 }}
          >
            数据暂时不可用
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ textAlign: 'center' }}
          >
            恐惧贪婪指数数据正在维护中
          </Typography>
        </Box>
      </FearGreedCard>
    );
  }

  const fearAndGreedValue = parseInt(fearGreedData.value, 10);
  const rangeInfo = getRangeInfo(fearAndGreedValue);

  return (
    <FearGreedCard rangeInfo={rangeInfo} sx={sx}>
      {/* 动态背景装饰 */}
      <Box
        sx={{
          position: 'absolute',
          top: -60,
          right: -60,
          width: { xs: 180, sm: 240 },
          height: { xs: 180, sm: 240 },
          background: `radial-gradient(circle, ${rangeInfo.color}10 0%, transparent 60%)`,
          borderRadius: '50%',
          animation: `${float} 8s ease-in-out infinite`,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: -40,
          left: -40,
          width: { xs: 100, sm: 140 },
          height: { xs: 100, sm: 140 },
          background: `radial-gradient(circle, ${rangeInfo.color}08 0%, transparent 70%)`,
          borderRadius: '50%',
          animation: `${float} 10s ease-in-out infinite reverse`,
        }}
      />

      {/* 标题区域 */}
      <StyledCardHeader
        title={
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: { xs: 1, sm: 2 },
            }}
          >
            <GradientTypography
              variant={isMobile ? 'h6' : 'h5'}
              sx={{
                fontSize: isMobile ? '1.2rem' : '1.4rem',
                fontWeight: 700,
              }}
            >
              市场情绪指数
            </GradientTypography>
            <Chip
              label={rangeInfo.zhLabel}
              size="small"
              sx={{
                backgroundColor: `${rangeInfo.color}20`,
                color: rangeInfo.color,
                fontWeight: 600,
                animation: `${pulse} 3s ease-in-out infinite`,
              }}
            />
          </Box>
        }
        action={
          <Tooltip title="查看数据来源 (CNN Business)" arrow>
            <IconButton
              size={isMobile ? 'small' : 'medium'}
              onClick={() =>
                window.open(
                  'https://edition.cnn.com/markets/fear-and-greed',
                  '_blank'
                )
              }
              aria-label="查看CNN恐惧贪婪指数"
              sx={{
                background: `
                  linear-gradient(135deg, 
                    rgba(255,255,255,0.95) 0%, 
                    rgba(248,250,252,0.98) 100%)`,
                backdropFilter: 'blur(12px)',
                border: '1px solid rgba(255,255,255,0.3)',
                borderRadius: '12px',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  background: `
                    linear-gradient(135deg, 
                      #1976D2 0%, 
                      #1565C0 100%)`,
                  color: 'white',
                  transform: 'scale(1.08)',
                  boxShadow: `0 8px 24px ${rangeInfo.color}30`,
                },
              }}
            >
              <Launch fontSize="small" />
            </IconButton>
          </Tooltip>
        }
      />

      <CardContent
        sx={{
          pt: 0,
          pb: { xs: 3, sm: 4 },
          px: { xs: 2, sm: 3 },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: isCompactLayout ? 'column' : 'row',
            alignItems: 'center',
            gap: { xs: 3, sm: 4, lg: 6 },
            width: '100%',
          }}
        >
          {/* 专业仪表盘 */}
          <Box
            sx={{
              flex: isCompactLayout ? 'none' : '0 0 auto',
              width: isCompactLayout ? '100%' : 'auto',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <ProfessionalGauge
              value={fearAndGreedValue}
              size={gaugeSize}
              timestamp={fearGreedData.timestamp}
              fearGreedData={fearGreedData}
            />
          </Box>

          {/* 市场分析区域 */}
          {!isCompactLayout && (
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 3,
                pl: 2,
              }}
            >
              <Paper
                elevation={1}
                sx={{
                  p: 3,
                  borderRadius: '16px',
                  background: `
                    linear-gradient(135deg, 
                      rgba(255,255,255,0.9) 0%, 
                      rgba(248,250,252,0.95) 100%)`,
                  backdropFilter: 'blur(8px)',
                }}
              >
                <Typography
                  variant="h6"
                  fontWeight={700}
                  sx={{ mb: 2, color: rangeInfo.color }}
                >
                  市场情绪解读
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: 'text.secondary', lineHeight: 1.7 }}
                >
                  {rangeInfo.description}
                </Typography>
              </Paper>
            </Box>
          )}
        </Box>
      </CardContent>
    </FearGreedCard>
  );
};

export default FearAndGreedSection;
