import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import StrategiesPage from '../../pages/StrategiesPage';

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}));

// Mock the config
vi.mock('../../constants/config', () => ({
  API_BASE_URL: 'http://localhost:5001',
  API_ENDPOINTS: {
    STRATEGIES_ANALYSIS: '/api/strategies/analysis',
    ACCOUNTS: '/api/accounts',
    PORTFOLIO_OVERVIEW: '/api/portfolio/overview',
    PREDICTIONS: '/api/predictions',
    STRATEGIES_STOCKS: '/api/strategies/stocks',
  },
}));

// Mock Chart.js components
vi.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
  Pie: () => <div data-testid="pie-chart">Pie Chart</div>,
}));

// Mock Chart.js
vi.mock('chart.js', () => ({
  Chart: {
    register: vi.fn(),
  },
  CategoryScale: vi.fn(),
  LinearScale: vi.fn(),
  PointElement: vi.fn(),
  LineElement: vi.fn(),
  BarElement: vi.fn(),
  ArcElement: vi.fn(),
  Title: vi.fn(),
  Tooltip: vi.fn(),
  Legend: vi.fn(),
}));

// Mock ECharts
vi.mock('echarts', () => ({
  init: vi.fn(() => ({
    setOption: vi.fn(),
    resize: vi.fn(),
    dispose: vi.fn(),
  })),
  registerTheme: vi.fn(),
}));

const WrappedStrategiesPage = () => (
  <BrowserRouter>
    <StrategiesPage />
  </BrowserRouter>
);

describe('StrategiesPage Metric Formatting', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    global.fetch = vi.fn();
  });

  // Define formatting rules for different metric types
  const metricFormatting = {
    convertToPercentage: [
      'annualized_return',
      'win_rate',
      'portfolio_vol',
      'max_drawdown',
      'concentration_risk',
      'volatility',
      'turnover',
      'alpha',
    ],
    highPrecision: ['portfolio_var'],
    ratioMetrics: [
      'sharpe_ratio',
      'sortino_ratio',
      'profit_loss_ratio',
      'beta',
      'information_ratio',
      'calmar_ratio',
    ],
  };

  const formatMetricValue = (key, value) => {
    if (value !== undefined && value !== null) {
      if (typeof value === 'number') {
        if (metricFormatting.convertToPercentage.includes(key)) {
          return `${(value * 100).toFixed(1)}%`;
        } else if (metricFormatting.highPrecision.includes(key)) {
          return value.toFixed(6);
        } else if (metricFormatting.ratioMetrics.includes(key)) {
          return value.toFixed(3);
        } else {
          return value.toFixed(3);
        }
      } else {
        return String(value);
      }
    }
    return '---';
  };

  // Mock data for testing
  const mockAccountsData = [{ account_id: 1, account_name: 'Test Account' }];

  const mockOverviewData = {
    total_value: 100000,
    total_cost: 80000,
    total_return: 20000,
    return_rate: 0.25,
  };

  const mockStocksData = {
    position_symbols: ['AAPL'],
    cached_symbols: ['AAPL', 'MSFT', 'GOOGL'],
  };

  const mockStrategiesData = {
    performance_metrics: {
      annualized_return: 0.808, // Should display as 80.8%
      win_rate: 0.65, // Should display as 65.0%
      sharpe_ratio: 1.552, // Should display as 1.552
      profit_loss_ratio: 2.1, // Should display as 2.100
      max_gain_info: 'AAPL, 25.5%, 2024-01-15',
      max_loss_info: 'MSFT, -15.2%, 2024-02-10',
      avg_holding_days: 45,
    },
    risk_metrics: {
      portfolio_var: 0.000055, // Should display as 0.000055
      portfolio_vol: 0.37274, // Should display as 37.3% (decimal from backend)
      max_drawdown: 0.33228, // Should display as 33.2% (decimal from backend)
      concentration_risk: 0.14821, // Should display as 14.8% (decimal from backend)
      sharpe_ratio: 1.552, // Should display as 1.552
      risk_level: 'HIGH',
    },
    positions: [
      {
        symbol: 'AAPL',
        shares: 100,
        avg_price: 150.0,
        current_price: 160.0,
        market_value: 16000,
        unrealized_gain: 1000,
        return_rate: 0.0667,
      },
    ],
    market_data: {
      AAPL: [
        { date: '2024-01-01', close: 150.0 },
        { date: '2024-01-02', close: 160.0 },
      ],
    },
  };

  const setupMockFetch = (strategiesData = mockStrategiesData) => {
    global.fetch = vi
      .fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockAccountsData),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOverviewData),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockStocksData),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(strategiesData),
      });
  };

  // Test the metric formatting logic directly
  describe('Metric Formatting Logic', () => {
    it('formats percentage metrics correctly', () => {
      expect(formatMetricValue('annualized_return', 0.808)).toBe('80.8%');
      expect(formatMetricValue('win_rate', 0.65)).toBe('65.0%');
      expect(formatMetricValue('portfolio_vol', 0.37274)).toBe('37.3%');
      expect(formatMetricValue('max_drawdown', 0.33228)).toBe('33.2%');
      expect(formatMetricValue('concentration_risk', 0.14821)).toBe('14.8%');
    });

    it('formats high precision metrics correctly', () => {
      expect(formatMetricValue('portfolio_var', 0.000055)).toBe('0.000055');
    });

    it('formats ratio metrics correctly', () => {
      expect(formatMetricValue('sharpe_ratio', 1.552)).toBe('1.552');
      expect(formatMetricValue('profit_loss_ratio', 2.1)).toBe('2.100');
      expect(formatMetricValue('beta', 1.15)).toBe('1.150');
    });

    it('handles edge cases correctly', () => {
      expect(formatMetricValue('annualized_return', null)).toBe('---');
      expect(formatMetricValue('win_rate', undefined)).toBe('---');
      expect(formatMetricValue('risk_level', 'HIGH')).toBe('HIGH');
    });

    it('specifically tests win_rate formatting fix', () => {
      // Backend now returns decimal values, frontend converts to percentage
      expect(formatMetricValue('win_rate', 0.65)).toBe('65.0%');
      expect(formatMetricValue('win_rate', 0.0)).toBe('0.0%');
      expect(formatMetricValue('win_rate', 1.0)).toBe('100.0%');
      expect(formatMetricValue('win_rate', 0.333)).toBe('33.3%');
    });
  });

  // Test component integration (simplified)
  describe('Component Integration', () => {
    it('renders without crashing', () => {
      setupMockFetch();
      const { container } = render(<WrappedStrategiesPage />);

      // Should render without throwing errors
      expect(container).toBeInTheDocument();
    });

    it('handles API errors gracefully', () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('API Error'));

      const { container } = render(<WrappedStrategiesPage />);

      // Should still render without throwing errors
      expect(container).toBeInTheDocument();
    });
  });

  // Test edge cases for metric formatting
  describe('Edge Cases', () => {
    it('handles zero values correctly', () => {
      expect(formatMetricValue('win_rate', 0)).toBe('0.0%');
      expect(formatMetricValue('portfolio_vol', 0)).toBe('0.0%');
      expect(formatMetricValue('sharpe_ratio', 0)).toBe('0.000');
    });

    it('handles very small values correctly', () => {
      expect(formatMetricValue('portfolio_var', 0.000001)).toBe('0.000001');
      expect(formatMetricValue('win_rate', 0.001)).toBe('0.1%');
    });

    it('handles very large values correctly', () => {
      expect(formatMetricValue('sharpe_ratio', 999.999)).toBe('999.999');
      expect(formatMetricValue('win_rate', 1.0)).toBe('100.0%');
    });

    it('handles negative values correctly', () => {
      expect(formatMetricValue('max_drawdown', -0.25)).toBe('-25.0%');
      expect(formatMetricValue('sharpe_ratio', -1.5)).toBe('-1.500');
    });
  });
});
