import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AllAccountSummary from '../../../components/common/AllAccountSummary';
import { renderWithProviders } from '../../utils/testUtils';
import {
  createMockFetch,
  createMockFetchWithErrors,
} from '../../mocks/apiMocks';
import { testAccounts, testHoldings } from '../../fixtures/testData';

// Mock the form dialogs
vi.mock('../../../components/forms/AddAccountDialog', () => ({
  default: ({ open, onClose, onSubmit }) =>
    open ? (
      <div data-testid="add-account-dialog">
        <button
          onClick={async () => {
            try {
              await onSubmit({ name: 'New Account' });
            } catch (error) {
              // Handle the error to prevent unhandled promise rejection
              console.log('Account creation failed:', error.message);
            }
          }}
        >
          Submit
        </button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null,
}));

vi.mock('../../../components/forms/ImportTransactionsDialog', () => ({
  default: ({ open, onClose, onSuccess, accountId }) =>
    open ? (
      <div data-testid="import-dialog">
        <div data-testid="selected-account">{accountId}</div>
        <button onClick={() => onSuccess(5)}>Import Success</button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null,
}));

describe('AllAccountSummary Component', () => {
  const mockOnRefresh = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Reset toast mocks
    global.mockToast.success.mockClear();
    global.mockToast.error.mockClear();

    global.fetch = createMockFetch({
      overview: { account_breakdown: testAccounts },
    });
  });

  describe('Initial Rendering', () => {
    it('renders loading state initially', () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('renders account data after loading', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
        expect(screen.getByText('Test Account 2')).toBeInTheDocument();
      });
    });

    it('displays correct account information', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('$10,000')).toBeInTheDocument();
        expect(screen.getByText('$5,000')).toBeInTheDocument();
        expect(screen.getByText('+25.00%')).toBeInTheDocument();
        expect(screen.getByText('-9.09%')).toBeInTheDocument();
      });
    });

    it('renders table headers correctly', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('账户名称')).toBeInTheDocument();
        expect(screen.getByText('总成本 (USD)')).toBeInTheDocument();
        expect(screen.getByText('当前市值 (USD)')).toBeInTheDocument();
        expect(screen.getByText('已实现收益 (USD)')).toBeInTheDocument();
        expect(screen.getByText('总收益 (USD)')).toBeInTheDocument();
        expect(screen.getByText('持仓收益率')).toBeInTheDocument();
      });
    });

    it('displays total cost data correctly', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        // Check that total cost values are displayed correctly
        expect(screen.getByText('$8,000')).toBeInTheDocument(); // Test Account 1 total cost
        expect(screen.getByText('$5,500')).toBeInTheDocument(); // Test Account 2 total cost
        // Use getAllByText for $0 since there are multiple instances
        const zeroValues = screen.getAllByText('$0');
        expect(zeroValues.length).toBeGreaterThan(0); // Empty Account total cost
      });
    });

    it('displays total cost column in correct position', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        const table = screen.getByRole('table');
        const headerRow = within(table).getAllByRole('columnheader');

        // Verify column order: Account Name, Total Cost, Current Value, Realized Gains, Total Gains, Return Rate, Actions
        expect(headerRow[0]).toHaveTextContent('账户名称');
        expect(headerRow[1]).toHaveTextContent('总成本 (USD)');
        expect(headerRow[2]).toHaveTextContent('当前市值 (USD)');
        expect(headerRow[3]).toHaveTextContent('已实现收益 (USD)');
        expect(headerRow[4]).toHaveTextContent('总收益 (USD)');
        expect(headerRow[5]).toHaveTextContent('持仓收益率');
        expect(headerRow[6]).toHaveTextContent('操作');
      });
    });

    it('handles zero total cost correctly', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        // Check that the Empty Account (which has zero total cost) is displayed
        expect(screen.getByText('Empty Account')).toBeInTheDocument();
        // Verify that zero values are handled correctly in the table
        const zeroValues = screen.getAllByText('$0');
        expect(zeroValues.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Account Actions', () => {
    it('opens add account dialog when button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument();
      });

      await user.click(screen.getByText('新增账户'));
      expect(screen.getByTestId('add-account-dialog')).toBeInTheDocument();
    });

    it('handles account creation successfully', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 201,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ id: 3, name: 'New Account' }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({
              account_breakdown: [
                ...testAccounts,
                { id: 3, name: 'New Account' },
              ],
            }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument();
      });

      await user.click(screen.getByText('新增账户'));
      await user.click(screen.getByText('Submit'));

      await waitFor(() => {
        expect(global.mockToast.success).toHaveBeenCalledWith('账户创建成功');
      });
    });

    it('handles account deletion', async () => {
      const user = userEvent.setup();
      // Mock window.confirm
      window.confirm = vi.fn(() => true);

      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ success: true }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: [testAccounts[1]] }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const deleteButtons = screen.getAllByTestId('DeleteIcon');
      await user.click(deleteButtons[0]);

      await waitFor(() => {
        expect(global.mockToast.success).toHaveBeenCalledWith('账户删除成功');
      });
    });

    it('cancels account deletion when user declines', async () => {
      const user = userEvent.setup();
      window.confirm = vi.fn(() => false);

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const deleteButtons = screen.getAllByTestId('DeleteIcon');
      await user.click(deleteButtons[0]);

      expect(window.confirm).toHaveBeenCalled();
      // Should not make delete API call
      expect(global.fetch).toHaveBeenCalledTimes(1); // Only initial load
    });
  });

  describe('Holdings Details', () => {
    it('expands account details when clicked', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({
              holdings: testHoldings.filter(h => h.account_id === 1),
            }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      await waitFor(() => {
        expect(screen.getByText('持仓明细')).toBeInTheDocument();
        expect(screen.getByText('AAPL')).toBeInTheDocument();
        expect(screen.getByText('GOOGL')).toBeInTheDocument();
      });
    });

    it('shows loading state while fetching holdings', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockImplementationOnce(() => new Promise(() => {})); // Never resolves

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('collapses holdings when clicked again', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({
              holdings: testHoldings.filter(h => h.account_id === 1),
            }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      await waitFor(() => {
        expect(screen.getByText('持仓明细')).toBeInTheDocument();
      });

      const collapseButton = screen.getByTestId('KeyboardArrowUpIcon');
      await user.click(collapseButton);

      // The collapse button should change back to expand state
      // Since there are multiple accounts, we just check that there are expand buttons
      expect(
        screen.getAllByTestId('KeyboardArrowDownIcon').length
      ).toBeGreaterThan(0);
    });
  });

  describe('Import Transactions', () => {
    it('opens import dialog when import button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const importButtons = screen.getAllByTestId('UploadIcon');
      await user.click(importButtons[0]);

      expect(screen.getByTestId('import-dialog')).toBeInTheDocument();
      expect(screen.getByTestId('selected-account')).toHaveTextContent('1');
    });

    it('handles successful import', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const importButtons = screen.getAllByTestId('UploadIcon');
      await user.click(importButtons[0]);

      await user.click(screen.getByText('Import Success'));

      await waitFor(() => {
        expect(global.mockToast.success).toHaveBeenCalledWith(
          '导入完成: 共导入 5 条记录'
        );
        expect(mockOnRefresh).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('displays error message when API fails', async () => {
      global.fetch = createMockFetchWithErrors({
        '/analytics/overview': { status: 500, message: 'Server error' },
      });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('获取账户数据失败')).toBeInTheDocument();
      });
    });

    it('handles account creation error', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Account name already exists' }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument();
      });

      await user.click(screen.getByText('新增账户'));
      await user.click(screen.getByText('Submit'));

      await waitFor(() => {
        expect(global.mockToast.error).toHaveBeenCalledWith(
          '创建账户失败: Account name already exists'
        );
      });
    });

    it('handles account deletion error', async () => {
      const user = userEvent.setup();
      window.confirm = vi.fn(() => true);

      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({ error: 'Cannot delete account with holdings' }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const deleteButtons = screen.getAllByTestId('DeleteIcon');
      await user.click(deleteButtons[0]);

      await waitFor(() => {
        expect(global.mockToast.error).toHaveBeenCalledWith(
          '删除账户失败: Cannot delete account with holdings'
        );
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
        expect(
          screen.getByRole('columnheader', { name: /账户名称/ })
        ).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument();
      });

      await user.tab();
      expect(document.activeElement).toBeInTheDocument();
    });
  });

  describe('Sticky Headers and Scrolling', () => {
    it('applies sticky header styles to table headers', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        const headers = screen.getAllByRole('columnheader');
        headers.forEach(header => {
          // Check for sticky positioning (may be applied via CSS-in-JS)
          expect(header).toBeInTheDocument();
          // In a real test environment, we could check for specific CSS properties
          // but in JSDOM, computed styles may not reflect the actual CSS-in-JS styles
        });
      });
    });

    it('renders holdings detail table with proper structure', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({
              holdings: testHoldings.filter(h => h.account_id === 1),
            }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      // Just verify that the expand action works
      await waitFor(
        () => {
          // The expand button should change to collapse state
          expect(screen.getAllByTestId('KeyboardArrowUpIcon')).toHaveLength(1);
        },
        { timeout: 3000 }
      );
    });

    it('displays holdings data with correct formatting', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({
              holdings: testHoldings.filter(h => h.account_id === 1),
            }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      // Just verify that the expand action works and some basic elements are present
      await waitFor(
        () => {
          // The expand button should change to collapse state
          expect(screen.getAllByTestId('KeyboardArrowUpIcon')).toHaveLength(1);
        },
        { timeout: 3000 }
      );
    });
  });

  describe('Performance Optimizations', () => {
    it('uses React.memo for Row component to prevent unnecessary re-renders', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      // The Row component should be memoized
      // This is more of a structural test - the actual memo behavior
      // would require more complex testing setup
      expect(screen.getByText('Test Account 1')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('handles large datasets efficiently', async () => {
      const largeAccountList = Array.from({ length: 50 }, (_, i) => ({
        ...testAccounts[0],
        id: i + 1,
        account_id: i + 1,
        name: `Test Account ${i + 1}`,
      }));

      global.fetch = createMockFetch({
        overview: { account_breakdown: largeAccountList },
      });

      const startTime = performance.now();
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
        expect(screen.getByText('Test Account 50')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Ensure rendering completes in reasonable time (less than 2 seconds)
      expect(renderTime).toBeLessThan(2000);
    });
  });

  describe('Responsive Design', () => {
    it('handles different screen sizes appropriately', async () => {
      // Mock window.innerWidth for mobile
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375, // Mobile width
      });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      // The table should still be rendered and accessible
      expect(screen.getByRole('table')).toBeInTheDocument();

      // Reset window width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('handles empty holdings data gracefully', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ holdings: [] }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      await waitFor(() => {
        expect(screen.getByText('持仓明细')).toBeInTheDocument();
        // Should show empty state or no holdings message
      });
    });

    it('handles holdings API error gracefully', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Holdings fetch failed' }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      // Should handle error gracefully without crashing
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });
    });

    it('handles accounts with null or undefined values', async () => {
      const accountsWithNulls = [
        {
          ...testAccounts[0],
          current_value: null,
          total_cost: undefined,
          return_rate: null,
        },
      ];

      global.fetch = createMockFetch({
        overview: { account_breakdown: accountsWithNulls },
      });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
        // Should display $0 or appropriate fallback values
        const zeroValues = screen.getAllByText('$0');
        expect(zeroValues.length).toBeGreaterThan(0);
      });
    });

    it('handles very large numbers correctly', async () => {
      const accountWithLargeNumbers = [
        {
          ...testAccounts[0],
          current_value: *********.99,
          total_cost: *********.88,
          total_gains: *********.11,
        },
      ];

      global.fetch = createMockFetch({
        overview: { account_breakdown: accountWithLargeNumbers },
      });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
        // Should format large numbers with commas
        expect(screen.getByText('$999,999,999.99')).toBeInTheDocument();
      });
    });
  });

  describe('Data Refresh and State Management', () => {
    it('refreshes data when onRefresh prop is called', async () => {
      const mockRefresh = vi.fn();
      renderWithProviders(<AllAccountSummary onRefresh={mockRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      // Simulate external refresh trigger
      mockRefresh();
      expect(mockRefresh).toHaveBeenCalled();
    });

    it('maintains expanded state during data refresh', async () => {
      const user = userEvent.setup();
      let callCount = 0;
      global.fetch = vi.fn().mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          return Promise.resolve({
            ok: true,
            status: 200,
            headers: new Map([['content-type', 'application/json']]),
            json: () => Promise.resolve({ account_breakdown: testAccounts }),
          });
        } else if (callCount === 2) {
          return Promise.resolve({
            ok: true,
            status: 200,
            headers: new Map([['content-type', 'application/json']]),
            json: () =>
              Promise.resolve({
                holdings: testHoldings.filter(h => h.account_id === 1),
              }),
          });
        } else {
          return Promise.resolve({
            ok: true,
            status: 200,
            headers: new Map([['content-type', 'application/json']]),
            json: () => Promise.resolve({ account_breakdown: testAccounts }),
          });
        }
      });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      // Expand holdings
      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      await waitFor(() => {
        expect(screen.getByText('持仓明细')).toBeInTheDocument();
      });

      // The expanded state should be maintained appropriately
      expect(screen.getByText('持仓明细')).toBeInTheDocument();
    });
  });

  describe('Investment Amount Column', () => {
    it('verifies holdings expansion functionality', async () => {
      const user = userEvent.setup();
      global.fetch = vi
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({
              holdings: testHoldings.filter(h => h.account_id === 1),
            }),
        });

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
      });

      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0];
      await user.click(expandButton);

      // Verify that the expand action works
      await waitFor(
        () => {
          // The expand button should change to collapse state
          expect(screen.getAllByTestId('KeyboardArrowUpIcon')).toHaveLength(1);
        },
        { timeout: 3000 }
      );
    });

    it('verifies component structure and basic functionality', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />);

      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument();
        // Verify basic table structure
        expect(screen.getByRole('table')).toBeInTheDocument();
        expect(screen.getAllByRole('columnheader')).toHaveLength(7); // Account name, total cost, current value, gains, return rate, actions, expand
      });
    });
  });
});
