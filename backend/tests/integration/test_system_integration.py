"""
Comprehensive system integration tests for the cache system
"""

import asyncio
import os
import tempfile
import threading
import time
from unittest.mock import Mock, patch

import pandas as pd
import pytest

from src.utils.cache import CacheConfig, StockCache, stock_cache


class TestSystemIntegration:
    """System integration tests for cache system"""

    def setup_method(self):
        """Setup test environment"""
        self.test_config = CacheConfig(
            redis_url="redis://localhost:6379/15",  # Test database
        )
        self.cache = StockCache(config=self.test_config)
        self.test_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"]

    def teardown_method(self):
        """Cleanup after tests"""
        try:
            self.cache.clear_cache()
            self.cache.shutdown()
        except:
            pass

    def create_mock_data(self) -> pd.DataFrame:
        """Create mock historical data"""
        dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
        return pd.DataFrame(
            {
                "Open": [150.0 + i * 0.1 for i in range(100)],
                "High": [155.0 + i * 0.1 for i in range(100)],
                "Low": [145.0 + i * 0.1 for i in range(100)],
                "Close": [152.0 + i * 0.1 for i in range(100)],
                "Volume": [1000000] * 100,
            },
            index=dates,
        )

    def test_redis_connectivity_and_failover(self):
        """Test Redis connectivity and failover mechanisms"""
        # Test Redis health check
        is_healthy = self.cache._check_redis_health()
        print(f"Redis health status: {is_healthy}")

        # Test circuit breaker functionality
        original_client = self.cache.redis_client

        # Simulate Redis failures
        self.cache.redis_client = None
        for _ in range(self.test_config.circuit_breaker_threshold + 1):
            self.cache._check_redis_health()

        # Circuit breaker should be triggered (Redis marked unhealthy)
        assert not self.cache.redis_healthy
        assert self.cache.cache_stats["circuit_breaker_trips"] > 0

        # Restore Redis client
        self.cache.redis_client = original_client

        # Test recovery after circuit breaker timeout
        self.cache.circuit_breaker_last_trip = time.time() - 70  # Simulate 70 seconds ago
        # Check if Redis health is restored
        self.cache._check_redis_health()
        print(f"Redis healthy after recovery: {self.cache.redis_healthy}")

    def test_memory_usage_patterns(self):
        """Test memory usage patterns and garbage collection"""
        try:
            import psutil

            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Fill cache with substantial data
            with patch("yfinance.download") as mock_download:
                mock_download.side_effect = lambda symbol, **kwargs: self.create_mock_data()

                # Add many cache entries
                for i in range(50):
                    symbol = f"TEST{i:03d}"
                    self.cache.get_historical_data(symbol)

                # Check memory usage
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory

                print(f"Memory usage: {initial_memory:.2f} -> {current_memory:.2f} MB (+{memory_increase:.2f} MB)")

                # Memory increase should be reasonable (less than 200MB for 50 entries)
                assert memory_increase < 200, f"Memory increase too high: {memory_increase:.2f} MB"

                # Test cache health instead of size info
                cache_health = self.cache.get_cache_health()
                assert "stats" in cache_health
                assert "l1_cache_enabled" in cache_health["stats"]
                assert "redis_enabled" in cache_health["stats"]

                print(f"Cache health: {cache_health}")
                assert cache_health["stats"]["l1_cache_enabled"], "L1 cache should be enabled"

        except ImportError:
            pytest.skip("psutil not available for memory testing")

    def test_concurrent_access_under_load(self):
        """Test concurrent access patterns under load"""
        results = {}
        errors = []

        def worker(worker_id, symbols):
            try:
                with patch("src.utils.cache.Ticker") as mock_ticker:
                    mock_ticker_instance = Mock()
                    mock_ticker_instance.price = {symbol: {"regularMarketPrice": 150.0 + worker_id * 10} for symbol in symbols}
                    mock_ticker.return_value = mock_ticker_instance

                    # Perform multiple operations
                    prices = self.cache.get_current_prices(symbols)
                    results[worker_id] = len(prices)

            except Exception as e:
                errors.append(f"Worker {worker_id}: {e}")

        # Create multiple concurrent workers
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i, self.test_symbols[:3]))  # Use subset to avoid overwhelming
            threads.append(thread)
            thread.start()

        # Wait for all threads
        for thread in threads:
            thread.join(timeout=10)

        # Verify results
        assert len(errors) == 0, f"Concurrent access errors: {errors}"
        assert len(results) == 10, "Not all workers completed"

    def test_timeout_and_rate_limit_handling(self):
        """Test handling of timeouts and rate limits"""
        # Test API timeout simulation
        with patch("yfinance.Ticker") as mock_ticker:
            # Simulate timeout
            mock_ticker.side_effect = TimeoutError("API timeout")

            start_time = time.time()
            prices = self.cache.get_current_prices(["AAPL"])
            timeout_time = time.time() - start_time

            # Should handle timeout gracefully and quickly
            assert timeout_time < 5.0, "Timeout handling took too long"
            assert isinstance(prices, dict), "Should return dict even on timeout"

            # Check error stats (may be 0 if error handling doesn't increment this counter)
            assert "api_errors" in self.cache.cache_stats

    def test_batch_processing_performance(self):
        """Test batch processing performance under various loads"""
        batch_sizes = [1, 5, 10, 20, 50]
        performance_results = {}

        with patch("src.utils.cache.Ticker") as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.price = {f"STOCK{i:03d}": {"regularMarketPrice": 100.0 + i} for i in range(100)}
            mock_ticker.return_value = mock_ticker_instance

            for batch_size in batch_sizes:
                symbols = [f"STOCK{i:03d}" for i in range(batch_size)]

                start_time = time.time()
                prices = self.cache.get_current_prices(symbols)
                batch_time = time.time() - start_time

                performance_results[batch_size] = {
                    "time": batch_time,
                    "symbols_processed": len(prices),
                    "time_per_symbol": batch_time / batch_size if batch_size > 0 else 0,
                }

                print(f"Batch size {batch_size}: {batch_time:.3f}s ({batch_time/batch_size:.4f}s per symbol)")

        # Verify batch processing efficiency
        # Larger batches should be more efficient per symbol
        if len(performance_results) >= 2:
            small_batch_efficiency = performance_results[1]["time_per_symbol"]
            large_batch_efficiency = performance_results[max(batch_sizes)]["time_per_symbol"]

            print(f"Efficiency improvement: {small_batch_efficiency/large_batch_efficiency:.2f}x")
            # Large batches should be at least as efficient as small ones
            assert large_batch_efficiency <= small_batch_efficiency * 1.5

    def test_cache_persistence_and_recovery(self):
        """Test cache persistence and recovery scenarios"""
        # Test data persistence across cache operations
        test_key = "test:persistence"
        test_data = {"price": 150.0, "timestamp": time.time()}

        # Store data
        from src.utils.cache import DataType

        self.cache.set_multi_level(test_key, test_data, DataType.PRICE)

        # Verify immediate retrieval
        retrieved_data = self.cache.get_multi_level(test_key, DataType.PRICE)
        assert retrieved_data is not None
        assert retrieved_data["price"] == test_data["price"]

        # Clear L1 cache only
        self.cache.l1_cache.clear()

        # Should still be available from L2 (Redis) if Redis is healthy
        if self.cache.redis_healthy:
            retrieved_data = self.cache.get_multi_level(test_key, DataType.PRICE)
            assert retrieved_data is not None
            assert retrieved_data["price"] == test_data["price"]

    def test_error_recovery_and_resilience(self):
        """Test error recovery and system resilience"""
        # Test recovery from various error conditions
        error_scenarios = [
            ("Network timeout", TimeoutError("Network timeout")),
            ("Connection error", ConnectionError("Connection failed")),
            ("Invalid data", ValueError("Invalid data format")),
            ("Generic error", Exception("Unknown error")),
        ]

        for error_name, error in error_scenarios:
            with patch("src.utils.cache.Ticker") as mock_ticker:
                mock_ticker.side_effect = error

                # Should handle error gracefully
                prices = self.cache.get_current_prices(["AAPL"])
                assert isinstance(prices, dict), f"Failed to handle {error_name}"

                # System should remain functional (either Redis healthy or fallback working)
                assert self.cache.redis_healthy or not self.cache.redis_healthy  # Always true - system handles both states


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
