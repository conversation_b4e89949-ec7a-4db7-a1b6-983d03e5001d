"""
Integration tests for the cache system across all application components
"""

import asyncio
import tempfile
from unittest.mock import Mock, patch

import pandas as pd
import pytest

from src.api.strategies import _get_market_data
from src.services.options_data import OptionsDataManager
from src.services.options_service import OptionsAnalysisService
from src.utils.cache import stock_cache


class TestCacheIntegration:
    """Integration tests for cache usage across the application"""

    def setup_method(self):
        """Setup test environment"""
        self.test_symbols = ["AAPL", "GOOGL", "MSFT"]
        self.mock_data = self.create_mock_data()

    def create_mock_data(self) -> pd.DataFrame:
        """Create mock historical data"""
        dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
        return pd.DataFrame(
            {
                "Open": [150.0 + i * 0.1 for i in range(100)],
                "High": [155.0 + i * 0.1 for i in range(100)],
                "Low": [145.0 + i * 0.1 for i in range(100)],
                "Close": [152.0 + i * 0.1 for i in range(100)],
                "Volume": [1000000] * 100,
            },
            index=dates,
        )

    def test_strategies_api_integration(self):
        """Test strategies API integration with cache"""
        with patch("src.utils.cache.stock_cache.get_historical_data_batch") as mock_batch:
            # Mock the batch data to return data for all symbols
            mock_data_dict = {}
            for symbol in self.test_symbols:
                mock_data_dict[symbol] = self.create_mock_data()

            mock_batch.return_value = mock_data_dict

            # Test the _get_market_data function
            market_data = _get_market_data(self.test_symbols)

            assert len(market_data) == len(self.test_symbols)
            for symbol in self.test_symbols:
                assert symbol in market_data
                assert not market_data[symbol].empty
                assert "Close" in market_data[symbol].columns

    def test_options_data_manager_integration(self):
        """Test OptionsDataManager integration with cache"""
        options_manager = OptionsDataManager()

        with patch("src.utils.cache.Ticker") as mock_ticker:
            # Mock price data
            mock_ticker_instance = Mock()
            mock_ticker_instance.price = {
                symbol: {"regularMarketPrice": 150.0 + i * 10} for i, symbol in enumerate(self.test_symbols)
            }
            mock_ticker.return_value = mock_ticker_instance

            # Test price fetching through options manager
            prices = options_manager._fetch_current_prices(self.test_symbols)

            assert len(prices) == len(self.test_symbols)
            for symbol in self.test_symbols:
                assert symbol in prices
                assert prices[symbol] > 0

    def test_options_service_integration(self):
        """Test OptionsAnalysisService integration with cache"""
        with tempfile.NamedTemporaryFile(suffix=".db") as temp_db:
            # Mock database operations
            with patch("src.services.options_service.get_db_connection") as mock_conn:
                mock_conn.return_value.__enter__.return_value.fetchall.return_value = []

                options_service = OptionsAnalysisService()

                # Test that the service uses the stock cache
                assert options_service.stock_cache == stock_cache

    def test_backward_compatibility(self):
        """Test backward compatibility with original StockCache interface"""
        # Test that stock_cache provides the same interface
        assert hasattr(stock_cache, "get_historical_data")
        assert hasattr(stock_cache, "get_current_prices")
        assert hasattr(stock_cache, "clear_cache")

        with patch("yfinance.download") as mock_download:
            mock_download.return_value = self.create_mock_data()

            # Test historical data method
            df = stock_cache.get_historical_data("AAPL")
            assert isinstance(df, pd.DataFrame)
            assert not df.empty

    def test_batch_optimization_benefits(self):
        """Test that batch processing provides performance benefits"""
        with patch("src.utils.cache.Ticker") as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.price = {
                symbol: {"regularMarketPrice": 150.0 + i * 10} for i, symbol in enumerate(self.test_symbols)
            }
            mock_ticker.return_value = mock_ticker_instance

            # Test batch price fetching
            prices = stock_cache.get_portfolio_prices(self.test_symbols)

            assert len(prices) == len(self.test_symbols)
            for symbol in self.test_symbols:
                assert symbol in prices
                assert prices[symbol] > 0

            # Verify that batch method was used (should be faster than individual calls)
            # This is tested implicitly through the mock setup

    def test_portfolio_data_batch_integration(self):
        """Test portfolio data batch functionality"""
        with patch("yfinance.download") as mock_download, patch("src.utils.cache.Ticker") as mock_ticker:

            # Setup mocks
            mock_download.side_effect = lambda symbol, **kwargs: self.create_mock_data()
            mock_ticker_instance = Mock()
            mock_ticker_instance.price = {
                symbol: {"regularMarketPrice": 150.0 + i * 10} for i, symbol in enumerate(self.test_symbols)
            }
            mock_ticker.return_value = mock_ticker_instance

            # Test batch price retrieval
            prices = stock_cache.get_portfolio_prices(self.test_symbols)
            assert len(prices) == len(self.test_symbols)

            # Test historical data batch retrieval
            historical_data = stock_cache.get_historical_data_batch(self.test_symbols)
            assert len(historical_data) == len(self.test_symbols)

    def test_error_handling_integration(self):
        """Test error handling across integrated components"""
        with patch("yfinance.download") as mock_download:
            # Simulate network error
            mock_download.side_effect = Exception("Network error")

            # Test that errors are handled gracefully
            df = stock_cache.get_historical_data("INVALID_SYMBOL")
            assert df.empty  # Should return empty DataFrame, not crash

    @pytest.mark.asyncio
    async def test_async_integration(self):
        """Test async functionality integration"""
        with patch("src.utils.cache.Ticker") as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.price = {
                symbol: {"regularMarketPrice": 150.0 + i * 10} for i, symbol in enumerate(self.test_symbols)
            }
            mock_ticker.return_value = mock_ticker_instance

            # Test batch operations
            prices = stock_cache.get_portfolio_prices(self.test_symbols)

            assert len(prices) == len(self.test_symbols)
            for symbol in self.test_symbols:
                assert symbol in prices

    def test_memory_efficiency_integration(self):
        """Test memory efficiency in integrated usage"""
        import os

        import psutil

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        with patch("yfinance.download") as mock_download:
            mock_download.side_effect = lambda symbol, **kwargs: self.create_mock_data()

            # Simulate heavy usage
            for _ in range(10):
                for symbol in self.test_symbols:
                    stock_cache.get_historical_data(symbol)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # Memory increase should be reasonable (less than 100MB for this test)
        assert memory_increase < 100, f"Memory increase too high: {memory_increase:.2f} MB"

    def test_concurrent_usage_integration(self):
        """Test concurrent usage across different components"""
        import threading
        import time

        results = {}
        errors = []

        # Apply the patch at the module level, outside the threads
        with patch("src.utils.cache.yf.Ticker") as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.history.return_value = self.create_mock_data()
            mock_ticker.return_value = mock_ticker_instance

            def worker(worker_id):
                try:
                    # Simulate concurrent access from different components
                    df = stock_cache.get_historical_data(f"TEST{worker_id}")
                    results[worker_id] = not df.empty
                except Exception as e:
                    errors.append(e)

            # Create multiple threads
            threads = []
            for i in range(5):
                thread = threading.Thread(target=worker, args=(i,))
                threads.append(thread)
                thread.start()

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            # Verify no errors and all workers succeeded
            assert len(errors) == 0, f"Errors in concurrent usage: {errors}"
            assert len(results) == 5
            assert all(results.values())

    def test_configuration_integration(self):
        """Test configuration integration across components"""
        # Test that cache configuration is properly applied
        cache_config = stock_cache.config

        assert cache_config.batch_size > 0
        assert cache_config.price_ttl > 0
        assert cache_config.historical_ttl > 0

        # Test that TTL settings are reasonable
        assert cache_config.price_ttl <= cache_config.historical_ttl  # Prices expire faster


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
