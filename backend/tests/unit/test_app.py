"""
Test cases for Flask application initialization and configuration.
"""

import os
import sys
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))


class TestFlaskAppInitialization:
    """Test cases for Flask application initialization."""

    def setup_method(self):
        """Setup test environment before each test."""
        # Reset any global state
        import src.app as app_module

        app_module.scheduler_initialized = False

    @patch.dict(os.environ, {"ALLOWED_ORIGINS": "http://localhost:3000,http://localhost:5173", "CORS_MAX_AGE": "1200"})
    def test_app_initialization_with_env_vars(self):
        """Test app initialization with environment variables."""
        # Import app after setting environment variables
        from src.app import app

        assert app is not None
        assert app.url_map.strict_slashes is False

    @patch.dict(os.environ, {}, clear=True)
    def test_app_initialization_with_defaults(self):
        """Test app initialization with default values."""
        # Clear the module cache to force re-import
        if "src.app" in sys.modules:
            del sys.modules["src.app"]

        from src.app import app

        assert app is not None
        assert app.url_map.strict_slashes is False

    def test_cors_configuration(self):
        """Test CORS configuration."""
        from src.app import app

        with app.test_client() as client:
            # Test OPTIONS request
            response = client.options("/api/accounts/")

            assert response.status_code in [200, 204]
            assert "Access-Control-Allow-Origin" in response.headers

    def test_preflight_request_handling(self):
        """Test preflight request handling."""
        from src.app import app

        with app.test_client() as client:
            # Test OPTIONS request with Origin header
            response = client.options("/api/accounts/", headers={"Origin": "http://localhost:3000"})

            assert response.status_code in [200, 204]
            assert response.headers.get("Access-Control-Allow-Origin") == "http://localhost:3000"
            assert "GET, POST, PUT, DELETE, OPTIONS, PATCH" in response.headers.get("Access-Control-Allow-Methods", "")

    def test_preflight_request_without_origin(self):
        """Test preflight request without Origin header."""
        from src.app import app

        with app.test_client() as client:
            response = client.options("/api/accounts/")

            assert response.status_code in [200, 204]

    @patch("src.app.start_scheduler")
    def test_scheduler_initialization_on_first_request(self, mock_start_scheduler):
        """Test scheduler initialization on first request."""
        from src.app import app

        with app.test_client() as client:
            # Make a request to trigger scheduler initialization
            client.get("/api/accounts/")

            mock_start_scheduler.assert_called_once()

    @patch("src.app.start_scheduler")
    def test_scheduler_not_initialized_twice(self, mock_start_scheduler):
        """Test scheduler is not initialized twice."""
        from src.app import app

        with app.test_client() as client:
            # Make multiple requests
            client.get("/api/accounts/")
            client.get("/api/accounts/")

            # Scheduler should only be started once
            mock_start_scheduler.assert_called_once()

    @patch("src.app.stop_scheduler")
    def test_cleanup_function_exists(self, mock_stop_scheduler):
        """Test cleanup function is registered."""
        from src.app import cleanup

        # Call cleanup function directly
        cleanup()

        mock_stop_scheduler.assert_called_once()

    def test_blueprints_registration(self):
        """Test that all blueprints are registered."""
        from src.app import app

        # Check that blueprints are registered
        blueprint_names = [bp.name for bp in app.blueprints.values()]

        expected_blueprints = ["accounts", "transactions", "analytics", "market", "strategies"]
        for expected in expected_blueprints:
            assert expected in blueprint_names

    def test_stock_cache_initialization(self):
        """Test stock cache initialization."""
        from src.app import stock_cache

        # Just verify that stock_cache exists and is not None
        assert stock_cache is not None

    def test_app_configuration_attributes(self):
        """Test app configuration attributes."""
        from src.app import app

        assert hasattr(app, "url_map")
        assert app.url_map.strict_slashes is False

    def test_cors_headers_in_response(self):
        """Test CORS headers are present in responses."""
        from src.app import app

        with app.test_client() as client:
            response = client.get("/api/accounts/")

            # Check for CORS headers (may be added by Flask-CORS)
            # The exact headers depend on the request and CORS configuration
            assert response.status_code in [200, 404, 500]  # Any valid HTTP status

    def test_dotenv_loading(self):
        """Test that dotenv loading doesn't cause errors."""
        # Since load_dotenv is called at module level, we can't easily mock it
        # Just verify the module imports successfully
        import src.app

        # Verify the app module loaded successfully
        assert hasattr(src.app, "app")

    def test_environment_variable_parsing(self):
        """Test environment variable parsing."""
        with patch.dict(os.environ, {"ALLOWED_ORIGINS": "http://localhost:3000,http://localhost:5173"}):
            # Re-import to trigger environment variable parsing
            if "src.app" in sys.modules:
                del sys.modules["src.app"]

            from src.app import ALLOWED_ORIGINS

            assert ALLOWED_ORIGINS == ["http://localhost:3000", "http://localhost:5173"]

    def test_cors_max_age_parsing(self):
        """Test CORS max age parsing."""
        with patch.dict(os.environ, {"CORS_MAX_AGE": "1800"}):
            # Re-import to trigger environment variable parsing
            if "src.app" in sys.modules:
                del sys.modules["src.app"]

            from src.app import CORS_MAX_AGE

            assert CORS_MAX_AGE == 1800

    def test_cors_max_age_default(self):
        """Test CORS max age default value."""
        with patch.dict(os.environ, {}, clear=True):
            # Re-import to trigger environment variable parsing
            if "src.app" in sys.modules:
                del sys.modules["src.app"]

            from src.app import CORS_MAX_AGE

            assert CORS_MAX_AGE == 600

    def test_request_methods_allowed(self):
        """Test that all required HTTP methods are allowed."""
        from src.app import app

        with app.test_client() as client:
            # Test different HTTP methods
            methods_to_test = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]

            for method in methods_to_test:
                if method == "OPTIONS":
                    response = client.options("/api/accounts/")
                    assert response.status_code in [200, 204]
                else:
                    # For other methods, we expect them to be handled by the routes
                    # Even if they return 404 or 405, they should be processed
                    response = client.open("/api/accounts/", method=method)
                    assert response.status_code in [200, 404, 405, 500]

    @patch("src.app.app.run")
    def test_main_execution(self, mock_run):
        """Test main execution block."""
        # Mock __name__ to be '__main__'
        with patch("src.app.__name__", "__main__"):
            # Re-import to trigger main block
            if "src.app" in sys.modules:
                del sys.modules["src.app"]

            import src.app

            # The run method should be called when script is run directly
            # Note: This test may not work as expected due to import behavior

    def test_global_scheduler_state(self):
        """Test global scheduler state management."""
        from src.app import scheduler_initialized

        # Initially should be False
        assert scheduler_initialized is False

    @patch("src.app.start_scheduler")
    def test_scheduler_initialization_state_change(self, mock_start_scheduler):
        """Test scheduler initialization changes global state."""
        import src.app as app_module

        # Reset state
        app_module.scheduler_initialized = False

        from src.app import app

        with app.test_client() as client:
            # Make a request to trigger scheduler initialization
            client.get("/api/accounts/")

            # State should be changed
            assert app_module.scheduler_initialized is True

    def test_multiple_imports_same_app(self):
        """Test that multiple imports return the same app instance."""
        from src.app import app as app1
        from src.app import app as app2

        assert app1 is app2

    @patch("src.app.atexit.register")
    def test_atexit_registration(self, mock_atexit_register):
        """Test that cleanup function is registered with atexit."""
        # Re-import to trigger atexit registration
        if "src.app" in sys.modules:
            del sys.modules["src.app"]

        import src.app

        mock_atexit_register.assert_called_once()
        # Verify the cleanup function was registered
        args, kwargs = mock_atexit_register.call_args
        assert callable(args[0])  # First argument should be the cleanup function
