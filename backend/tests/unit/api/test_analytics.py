"""
Unit tests for the analytics API endpoints.
"""

import json
from unittest.mock import Mock, patch

import pandas as pd
import pytest

from tests.fixtures.sample_data import SAMPLE_ANALYTICS, SAMPLE_PORTFOLIO
from tests.utils.test_helpers import APITestHelper, DatabaseTestHelper


class TestAnalyticsAPI:
    """Test cases for analytics API endpoints."""

    def test_get_overview_empty_database(self, client, test_db):
        """Test overview endpoint with empty database."""
        response = client.get("/api/analytics/overview")
        data = APITestHelper.assert_json_response(response, 200)

        assert data["total_return"] == 0
        assert data["account_breakdown"] == []

    @patch("src.api.analytics.get_stock_cache")
    def test_get_overview_with_data(self, mock_cache, client, test_db):
        """Test overview endpoint with account data using Redis cache."""
        # Create test account and data
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn, account_name="Test Account")

        # Price data now comes from Redis cache (mocked in test)

        # Create transaction
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )

        conn.commit()
        conn.close()

        # Mock Redis cache to return price data
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 175.0}

        response = client.get("/api/analytics/overview")
        data = APITestHelper.assert_json_response(response, 200)

        assert "total_return" in data
        assert "account_breakdown" in data
        assert len(data["account_breakdown"]) == 1

    def test_get_holdings_with_daily_returns_data(self, client, test_db):
        """Test holdings endpoint with daily returns data."""
        # Create test data
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)

        # Add daily returns data (using correct column name)
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO daily_returns (account_id, date, return_rate)
            VALUES (?, ?, ?)
            """,
            (account_data["account_id"], "2024-01-01", 0.02),
        )
        conn.commit()
        conn.close()

        response = client.get("/api/analytics/holdings")
        data = APITestHelper.assert_json_response(response, 200)

        assert "holdings" in data
        assert isinstance(data["holdings"], list)

    @patch("src.api.analytics.get_stock_cache")
    def test_get_holdings_with_account_filter_and_data(self, mock_cache, client, test_db):
        """Test holdings endpoint with account filter and data using Redis cache."""
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)

        # Price data now comes from Redis cache (mocked in test)

        # Create transaction
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )

        conn.commit()
        conn.close()

        # Mock Redis cache to return price data
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 175.0}

        response = client.get(f"/api/analytics/holdings?account_id={account_data['account_id']}")
        data = APITestHelper.assert_json_response(response, 200)

        assert "holdings" in data
        assert isinstance(data["holdings"], list)

    def test_get_holdings_database_error(self, client, test_db):
        """Test holdings endpoint with database error."""
        with patch("src.api.analytics.get_db_connection") as mock_get_conn:
            mock_conn = Mock()
            mock_conn.execute.side_effect = Exception("Database query failed")
            mock_get_conn.return_value = mock_conn

            response = client.get("/api/analytics/holdings")
            # The exception propagates up and Flask handles it as a 500 error
            assert response.status_code == 500

    @patch("src.api.analytics.get_stock_cache")
    def test_get_overview_with_top_stocks_data(self, mock_cache, client, test_db):
        """Test overview endpoint includes top stocks data using Redis cache."""
        # Create test data
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)

        # Price data now comes from Redis cache (mocked in test)

        # Create transaction
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )

        conn.commit()
        conn.close()

        # Mock Redis cache to return price data
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 175.0}

        response = client.get("/api/analytics/overview")
        data = APITestHelper.assert_json_response(response, 200)

        assert "account_breakdown" in data
        if data["account_breakdown"]:
            assert "top_stocks" in data["account_breakdown"][0]

        assert "total_return" in data
        assert "account_breakdown" in data
        assert len(data["account_breakdown"]) == 1

        account = data["account_breakdown"][0]
        assert account["name"] == "Test Account"
        # Current value will depend on Redis cache mock
        assert "total_cost" in account
        assert "top_stocks" in account

    def test_get_overview_with_total_cost_calculation(self, client, test_db):
        """Test overview endpoint includes total_cost in account breakdown."""
        # Create test account
        conn = DatabaseTestHelper.get_connection(test_db)
        account = DatabaseTestHelper.create_test_account(conn, account_name="Test Account")
        conn.close()

        with patch("src.api.analytics.calculate_current_market_value") as mock_calc:
            # Mock return: (current_value, total_cost, return_rate)
            mock_calc.return_value = (15000.0, 12000.0, 0.25)

            response = client.get("/api/analytics/overview")
            data = APITestHelper.assert_json_response(response, 200)

            assert len(data["account_breakdown"]) == 1
            account_data = data["account_breakdown"][0]

            # Verify all required fields are present
            assert "account_id" in account_data
            assert "name" in account_data
            assert "current_value" in account_data
            assert "total_cost" in account_data
            assert "realized_gains" in account_data
            assert "total_gains" in account_data
            assert "return_rate" in account_data
            assert "top_stocks" in account_data

            # Verify total_cost is correctly calculated and formatted
            assert account_data["total_cost"] == 12000.0
            assert account_data["current_value"] == 15000.0
            assert account_data["return_rate"] == 0.25

    def test_get_overview_with_account_filter(self, client, test_db):
        """Test overview endpoint with account_id filter."""
        # Create multiple test accounts
        conn = DatabaseTestHelper.get_connection(test_db)
        account1 = DatabaseTestHelper.create_test_account(conn, account_name="Account 1")
        account2 = DatabaseTestHelper.create_test_account(conn, account_name="Account 2")
        conn.close()

        with patch("src.api.analytics.calculate_current_market_value") as mock_calc:
            mock_calc.return_value = (10000.0, 8000.0, 0.25)

            # Test filtering by account_id
            response = client.get(f'/api/analytics/overview?account_id={account1["account_id"]}')
            data = APITestHelper.assert_json_response(response, 200)

            assert len(data["account_breakdown"]) == 1
            assert data["account_breakdown"][0]["account_id"] == account1["account_id"]
            assert data["account_breakdown"][0]["total_cost"] == 8000.0

    def test_get_overview_with_zero_total_cost(self, client, test_db):
        """Test overview endpoint handles zero total cost correctly."""
        # Create test account
        conn = DatabaseTestHelper.get_connection(test_db)
        account = DatabaseTestHelper.create_test_account(conn, account_name="Zero Cost Account")
        conn.close()

        with patch("src.api.analytics.calculate_current_market_value") as mock_calc:
            # Mock return: (current_value, total_cost, return_rate) with zero cost
            mock_calc.return_value = (0.0, 0.0, 0.0)

            response = client.get("/api/analytics/overview")
            data = APITestHelper.assert_json_response(response, 200)

            assert len(data["account_breakdown"]) == 1
            account_data = data["account_breakdown"][0]

            # Verify zero values are handled correctly
            assert account_data["total_cost"] == 0.0
            assert account_data["current_value"] == 0.0
            assert account_data["return_rate"] == 0.0

    def test_get_overview_invalid_account_filter(self, client, test_db):
        """Test overview endpoint with invalid account_id filter."""
        response = client.get("/api/analytics/overview?account_id=999")
        data = APITestHelper.assert_json_response(response, 200)

        assert data["total_return"] == 0
        assert data["account_breakdown"] == []

    @patch("pandas.read_sql")
    def test_get_overview_database_error(self, mock_read_sql, client, test_db):
        """Test overview endpoint with database error."""
        mock_read_sql.side_effect = Exception("Database error")

        response = client.get("/api/analytics/overview")
        APITestHelper.assert_error_response(response, 500)

    def test_get_holdings_empty_database(self, client, test_db):
        """Test holdings endpoint with empty database."""
        response = client.get("/api/analytics/holdings")
        data = APITestHelper.assert_json_response(response, 200)

        assert data["holdings"] == []

    def test_get_holdings_with_account_filter(self, client, test_db):
        """Test holdings endpoint with account filter."""
        # Create test data
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)
        DatabaseTestHelper.create_test_transaction(conn, account_id=str(account_data["account_id"]))
        conn.close()

        response = client.get(f'/api/analytics/holdings?account_id={account_data["account_id"]}')
        data = APITestHelper.assert_json_response(response, 200)

        assert "holdings" in data
        # Holdings might be empty if no cached prices exist, which is expected in tests

    def test_get_holdings_with_symbol_filter(self, client, test_db):
        """Test holdings endpoint with symbol filter."""
        response = client.get("/api/analytics/holdings?symbol=AAPL")
        data = APITestHelper.assert_json_response(response, 200)

        assert "holdings" in data

    def test_get_holdings_with_multiple_filters(self, client, test_db):
        """Test holdings endpoint with multiple filters."""
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)
        conn.close()

        response = client.get(f'/api/analytics/holdings?account_id={account_data["account_id"]}&symbol=AAPL')
        data = APITestHelper.assert_json_response(response, 200)

        assert "holdings" in data

    def test_get_realized_gains_missing_account_id(self, client, test_db):
        """Test realized gains endpoint without account_id parameter."""
        response = client.get("/api/analytics/realized_gains")
        APITestHelper.assert_error_response(response, 400, "Missing account_id parameter")

    def test_get_realized_gains_invalid_account_id(self, client, test_db):
        """Test realized gains endpoint with invalid account_id."""
        response = client.get("/api/analytics/realized_gains?account_id=abc")
        assert response.status_code == 400  # Should fail type conversion

    def test_get_realized_gains_empty_data(self, client, test_db):
        """Test realized gains endpoint with no realized gains."""
        response = client.get("/api/analytics/realized_gains?account_id=1")
        data = APITestHelper.assert_json_response(response, 200)

        assert "realized_gains" in data
        assert data["realized_gains"] == []
        # Note: API doesn't return total_realized_gain field

    def test_get_realized_gains_with_data(self, client, test_db):
        """Test realized gains endpoint with sample data."""
        # Create test account and add realized gains data
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)

        # Insert sample realized gains
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO realized_gains (account_id, symbol, buy_date, sell_date,
                                      buy_quantity, buy_price, sell_price, realized_gain)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (account_data["account_id"], "AAPL", "2023-01-01", "2023-02-01", 100, 150.0, 155.0, 500.0),
        )
        conn.commit()
        conn.close()

        response = client.get(f'/api/analytics/realized_gains?account_id={account_data["account_id"]}')
        data = APITestHelper.assert_json_response(response, 200)

        assert "realized_gains" in data
        assert len(data["realized_gains"]) == 1
        assert data["realized_gains"][0]["symbol"] == "AAPL"
        assert data["realized_gains"][0]["realized_gain"] == 500.0
        # Note: API doesn't return total_realized_gain field

    @patch("src.api.analytics.get_db_connection")
    def test_get_realized_gains_database_error(self, mock_db, client):
        """Test realized gains endpoint with database error."""
        mock_db.side_effect = Exception("Database connection failed")

        response = client.get("/api/analytics/realized_gains?account_id=1")
        APITestHelper.assert_error_response(response, 500)

    def test_analytics_endpoints_methods(self, client, test_db):
        """Test that only GET methods are allowed on analytics endpoints."""
        endpoints = ["/api/analytics/overview", "/api/analytics/holdings", "/api/analytics/realized_gains?account_id=1"]

        for endpoint in endpoints:
            # GET should work
            response = client.get(endpoint)
            assert response.status_code in [200, 400, 500]  # Should not be 405

            # Other methods should return 405
            response = client.post(endpoint)
            assert response.status_code == 405

            response = client.put(endpoint)
            assert response.status_code == 405

            response = client.delete(endpoint)
            assert response.status_code == 405

    @patch("src.api.analytics.get_stock_cache")
    def test_overview_return_rate_calculation(self, mock_cache, client, test_db):
        """Test that return rate is calculated correctly using Redis cache."""
        # Test with real data instead of mocks
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)

        # Price data now comes from Redis cache (mocked in test)

        # Create transaction
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )

        conn.commit()
        conn.close()

        # Mock Redis cache to return price data
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 175.0}

        response = client.get("/api/analytics/overview")
        data = APITestHelper.assert_json_response(response, 200)

        if data["account_breakdown"]:
            account = data["account_breakdown"][0]
            # Current value will depend on Redis cache mock
            assert "current_value" in account

    def test_holdings_structure_validation(self, client, test_db):
        """Test that holdings response has correct structure."""
        response = client.get("/api/analytics/holdings")
        data = APITestHelper.assert_json_response(response, 200)

        assert "holdings" in data
        assert isinstance(data["holdings"], list)

        # If holdings exist, validate structure
        for holding in data["holdings"]:
            required_fields = [
                "account_name",
                "symbol",
                "quantity",
                "current_price",
                "cost_per_share",
                "cost",
                "value",
                "gains",
                "return_rate",
            ]
            for field in required_fields:
                assert field in holding, f"Missing field: {field}"

    def test_holdings_redis_cache_integration(self, client, test_db):
        """Test holdings endpoint uses Redis cache for prices."""
        from unittest.mock import patch

        # Add test data
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn, account_name="Test Account")
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )
        conn.commit()
        conn.close()

        # Mock Redis cache to return specific prices
        with patch("src.api.analytics.get_stock_cache") as mock_cache:
            mock_cache_instance = Mock()
            mock_cache.return_value = mock_cache_instance
            mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 160.0}

            response = client.get("/api/analytics/holdings")
            data = APITestHelper.assert_json_response(response, 200)

            # Verify cache was called
            mock_cache_instance.get_portfolio_prices.assert_called_once()

            if data["holdings"]:
                holding = data["holdings"][0]
                assert holding["current_price"] == 160.0  # Price from Redis cache

    def test_holdings_price_calculation_accuracy(self, client, test_db):
        """Test holdings price calculation accuracy."""
        from unittest.mock import patch

        # Add test data with specific values
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn, account_name="Test Account")
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )
        conn.commit()
        conn.close()

        # Mock Redis cache to return specific price
        with patch("src.api.analytics.get_stock_cache") as mock_cache:
            mock_cache_instance = Mock()
            mock_cache.return_value = mock_cache_instance
            mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 160.0}

            response = client.get("/api/analytics/holdings")
            data = APITestHelper.assert_json_response(response, 200)

            if data["holdings"]:
                holding = data["holdings"][0]

                # Verify calculations
                assert holding["quantity"] == 100
                assert holding["cost_per_share"] == 150.0
                assert holding["cost"] == 15000.0  # 100 * 150
                assert holding["current_price"] == 160.0
                assert holding["value"] == 16000.0  # 100 * 160
                assert holding["gains"] == 1000.0  # 16000 - 15000
                assert abs(holding["return_rate"] - 0.*****************) < 0.001  # 1000/15000

    def test_holdings_empty_cache_fallback(self, client, test_db):
        """Test holdings endpoint when cache returns empty prices."""
        from unittest.mock import patch

        # Add test data
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn, account_name="Test Account")
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )
        conn.commit()
        conn.close()

        # Mock Redis cache to return empty prices
        with patch("src.api.analytics.get_stock_cache") as mock_cache:
            mock_cache_instance = Mock()
            mock_cache.return_value = mock_cache_instance
            mock_cache_instance.get_portfolio_prices.return_value = {}  # Empty cache

            response = client.get("/api/analytics/holdings")
            data = APITestHelper.assert_json_response(response, 200)

            if data["holdings"]:
                holding = data["holdings"][0]
                assert holding["current_price"] == 0.0  # Default when no price found
                assert holding["value"] == 0.0  # 100 * 0
                assert holding["gains"] == -15000.0  # 0 - 15000

    def test_holdings_sorting_by_value(self, client, test_db):
        """Test holdings are sorted by market value in descending order."""
        from unittest.mock import patch

        # Add test data with multiple holdings
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn, account_name="Test Account")

        # Create multiple transactions
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="AAPL", quantity=100, price=150.0
        )
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data["account_id"]), symbol="GOOGL", quantity=10, price=2000.0
        )
        conn.commit()
        conn.close()

        # Mock Redis cache with different prices
        with patch("src.api.analytics.get_stock_cache") as mock_cache:
            mock_cache_instance = Mock()
            mock_cache.return_value = mock_cache_instance
            mock_cache_instance.get_portfolio_prices.return_value = {
                "AAPL": 160.0,  # 100 * 160 = 16,000
                "GOOGL": 2200.0,  # 10 * 2200 = 22,000
            }

            response = client.get("/api/analytics/holdings")
            data = APITestHelper.assert_json_response(response, 200)

            if len(data["holdings"]) >= 2:
                # Should be sorted by value descending
                assert data["holdings"][0]["symbol"] == "GOOGL"  # Higher value
                assert data["holdings"][1]["symbol"] == "AAPL"  # Lower value
                assert data["holdings"][0]["value"] > data["holdings"][1]["value"]
