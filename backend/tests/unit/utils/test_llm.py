"""
Unit tests for the LLM service module.
"""

import json
import os
import tempfile
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, mock_open, patch

import pytest

from tests.utils.test_helpers import APITestHelper


class TestLLMService:
    """Test cases for LLM service functionality."""

    def test_read_env_file_default_config(self):
        """Test read_env_file returns default config when no .env file exists."""
        from src.utils.llm import read_env_file

        with patch("os.path.exists", return_value=False):
            config = read_env_file()

            assert config["LLM_API_ENDPOINT"] == "https://api.deepseek.com"
            assert config["LLM_API_KEY"] == ""
            assert config["LLM_MODEL"] == "deepseek-chat"

    def test_read_env_file_with_valid_file(self):
        """Test read_env_file reads configuration from .env file."""
        from src.utils.llm import read_env_file

        env_content = "LLM_API_ENDPOINT=https://api.test.com\nLLM_API_KEY=test_key_123\nLLM_MODEL=test-model\n# This is a comment\nOTHER_VAR=ignored\n"

        # Mock os.path.exists to return True for the first path checked
        def mock_exists(path):
            # Return True for the first path in the possible_paths list
            return path.endswith(".env")

        with patch("os.path.exists", side_effect=mock_exists), patch("builtins.open", mock_open(read_data=env_content)):
            config = read_env_file()

            assert config["LLM_API_ENDPOINT"] == "https://api.test.com"
            assert config["LLM_API_KEY"] == "test_key_123"
            assert config["LLM_MODEL"] == "test-model"

    def test_read_env_file_with_quotes(self):
        """Test read_env_file handles quoted values correctly."""
        from src.utils.llm import read_env_file

        env_content = (
            "LLM_API_ENDPOINT=\"https://api.quoted.com\"\nLLM_API_KEY='single_quoted_key'\nLLM_MODEL=unquoted_model\n"
        )

        with patch("os.path.exists", return_value=True), patch("builtins.open", mock_open(read_data=env_content)):
            config = read_env_file()

            assert config["LLM_API_ENDPOINT"] == "https://api.quoted.com"
            assert config["LLM_API_KEY"] == "single_quoted_key"
            assert config["LLM_MODEL"] == "unquoted_model"

    def test_read_env_file_error_handling(self):
        """Test read_env_file handles file read errors gracefully."""
        from src.utils.llm import read_env_file

        with (
            patch("os.path.exists", return_value=True),
            patch("builtins.open", side_effect=IOError("Permission denied")),
        ):
            config = read_env_file()

            # Should return default config on error
            assert config["LLM_API_ENDPOINT"] == "https://api.deepseek.com"
            assert config["LLM_API_KEY"] == ""
            assert config["LLM_MODEL"] == "deepseek-chat"

    def test_normalize_api_endpoint_removes_path(self):
        """Test normalize_api_endpoint removes /v1/chat/completions path."""
        from src.utils.llm import normalize_api_endpoint

        # Test with path that should be removed
        url = "https://api.test.com/v1/chat/completions"
        result = normalize_api_endpoint(url)
        assert result == "https://api.test.com"

        # Test with trailing slash
        url = "https://api.test.com/"
        result = normalize_api_endpoint(url)
        assert result == "https://api.test.com"

        # Test with both path and trailing slash
        url = "https://api.test.com/v1/chat/completions/"
        result = normalize_api_endpoint(url)
        assert result == "https://api.test.com"

        # Test with clean URL
        url = "https://api.test.com"
        result = normalize_api_endpoint(url)
        assert result == "https://api.test.com"

        # Test with empty URL
        result = normalize_api_endpoint("")
        assert result == ""

        # Test with None
        result = normalize_api_endpoint(None)
        assert result is None

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_llm_service_initialization(self, mock_openai):
        """Test LLMService initialization."""
        from src.utils.llm import LLMService

        mock_client = Mock()
        mock_openai.return_value = mock_client

        service = LLMService()

        # Verify OpenAI client was created with correct parameters
        mock_openai.assert_called_once_with(api_key="test_key", base_url="https://api.test.com")
        assert service.client == mock_client

    def test_ensure_json_format_valid_json(self):
        """Test ensure_json_format with valid JSON."""
        from src.utils.llm import LLMService

        valid_json = '{"key": "value", "number": 123}'
        result = LLMService.ensure_json_format(valid_json)

        assert result == {"key": "value", "number": 123}

    def test_ensure_json_format_invalid_json_with_extraction(self):
        """Test ensure_json_format extracts JSON from mixed content."""
        from src.utils.llm import LLMService

        mixed_content = 'Some text before {"key": "value", "valid": true} some text after'
        result = LLMService.ensure_json_format(mixed_content)

        assert result == {"key": "value", "valid": True}

    def test_ensure_json_format_with_trailing_comma(self):
        """Test ensure_json_format fixes trailing commas."""
        from src.utils.llm import LLMService

        json_with_comma = 'Text {"key": "value", "array": [1, 2, 3,], "trailing": "comma",} text'
        result = LLMService.ensure_json_format(json_with_comma)

        assert result == {"key": "value", "array": [1, 2, 3], "trailing": "comma"}

    def test_ensure_json_format_completely_invalid(self):
        """Test ensure_json_format returns empty dict for invalid content."""
        from src.utils.llm import LLMService

        invalid_content = "This is not JSON at all, no braces or anything"
        result = LLMService.ensure_json_format(invalid_content)

        assert result == {}

    def test_ensure_json_format_malformed_json(self):
        """Test ensure_json_format handles malformed JSON."""
        from src.utils.llm import LLMService

        malformed_json = '{"key": "value", "unclosed": "quote}'
        result = LLMService.ensure_json_format(malformed_json)

        assert result == {}

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.LLM_MODEL", "test-model")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_success(self, mock_openai):
        """Test successful LLM API call."""
        from src.utils.llm import LLMService

        # Mock OpenAI client and response
        mock_client = Mock()
        mock_openai.return_value = mock_client

        mock_response = Mock()
        mock_choice = Mock()
        mock_message = Mock()
        mock_message.content = '{"result": "success"}'
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]

        mock_client.chat.completions.create.return_value = mock_response

        # Test the API call
        result = LLMService.call_llm_api("Test prompt")

        assert result == '{"result": "success"}'

        # Verify the API was called with correct parameters
        mock_client.chat.completions.create.assert_called_once()
        call_args = mock_client.chat.completions.create.call_args
        assert call_args[1]["model"] == "test-model"
        assert call_args[1]["stream"] is False
        assert len(call_args[1]["messages"]) == 2
        assert call_args[1]["messages"][0]["role"] == "system"
        assert call_args[1]["messages"][1]["role"] == "user"
        assert call_args[1]["messages"][1]["content"] == "Test prompt"

    @patch("src.utils.llm.LLM_API_KEY", "")
    def test_call_llm_api_no_api_key(self):
        """Test LLM API call with missing API key."""
        from src.utils.llm import LLMService

        result = LLMService.call_llm_api("Test prompt")

        assert result == "API密钥未配置，无法调用LLM服务"

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "")
    def test_call_llm_api_no_endpoint(self):
        """Test LLM API call with missing endpoint."""
        from src.utils.llm import LLMService

        result = LLMService.call_llm_api("Test prompt")

        assert result == "API端点未配置，无法调用LLM服务"

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_empty_response(self, mock_openai):
        """Test LLM API call with empty response."""
        from src.utils.llm import LLMService

        # Mock OpenAI client with empty response
        mock_client = Mock()
        mock_openai.return_value = mock_client

        mock_response = Mock()
        mock_response.choices = []

        mock_client.chat.completions.create.return_value = mock_response

        result = LLMService.call_llm_api("Test prompt")

        assert result == "API调用失败: 响应无内容"

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_timeout_error(self, mock_openai):
        """Test LLM API call with timeout error."""
        import requests

        from src.utils.llm import LLMService

        # Mock OpenAI client to raise timeout
        mock_client = Mock()
        mock_openai.return_value = mock_client
        mock_client.chat.completions.create.side_effect = requests.exceptions.Timeout()

        result = LLMService.call_llm_api("Test prompt")

        assert result == "API调用超时，请稍后再试"

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_connection_error(self, mock_openai):
        """Test LLM API call with connection error."""
        import requests

        from src.utils.llm import LLMService

        # Mock OpenAI client to raise connection error
        mock_client = Mock()
        mock_openai.return_value = mock_client
        mock_client.chat.completions.create.side_effect = requests.exceptions.ConnectionError()

        result = LLMService.call_llm_api("Test prompt")

        assert result == "无法连接到API服务，请检查网络连接"

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_generic_error(self, mock_openai):
        """Test LLM API call with generic error."""
        from src.utils.llm import LLMService

        # Mock OpenAI client to raise generic exception
        mock_client = Mock()
        mock_openai.return_value = mock_client
        mock_client.chat.completions.create.side_effect = Exception("Generic error")

        result = LLMService.call_llm_api("Test prompt")

        assert result == "发生错误: Generic error"

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_json_success(self, mock_openai):
        """Test successful LLM API JSON call."""
        from src.utils.llm import LLMService

        # Mock OpenAI client and response
        mock_client = Mock()
        mock_openai.return_value = mock_client

        mock_response = Mock()
        mock_choice = Mock()
        mock_message = Mock()
        mock_message.content = '{"analysis": "test", "score": 8.5}'
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]

        mock_client.chat.completions.create.return_value = mock_response

        service = LLMService()
        result = service.call_llm_api_json("Test prompt")

        assert result == {"analysis": "test", "score": 8.5}

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_json_invalid_response(self, mock_openai):
        """Test LLM API JSON call with invalid JSON response."""
        from src.utils.llm import LLMService

        # Mock OpenAI client and response
        mock_client = Mock()
        mock_openai.return_value = mock_client

        mock_response = Mock()
        mock_choice = Mock()
        mock_message = Mock()
        mock_message.content = "This is not valid JSON"
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]

        mock_client.chat.completions.create.return_value = mock_response

        service = LLMService()
        result = service.call_llm_api_json("Test prompt")

        assert result == {}

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com")
    @patch("src.utils.llm.OpenAI")
    def test_call_llm_api_json_with_custom_params(self, mock_openai):
        """Test LLM API JSON call with custom temperature and max_tokens."""
        from src.utils.llm import LLMService

        # Mock OpenAI client and response
        mock_client = Mock()
        mock_openai.return_value = mock_client

        mock_response = Mock()
        mock_choice = Mock()
        mock_message = Mock()
        mock_message.content = '{"result": "custom_params"}'
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]

        mock_client.chat.completions.create.return_value = mock_response

        service = LLMService()
        result = service.call_llm_api_json("Test prompt", temperature=0.5, max_tokens=2000)

        assert result == {"result": "custom_params"}

        # Verify the call was made (parameters are passed to call_llm_api)
        mock_client.chat.completions.create.assert_called_once()

    def test_llm_service_integration_with_strategies(self):
        """Test LLMService integration with strategies module."""
        from src.utils.llm import LLMService

        with patch.object(LLMService, "call_llm_api_json") as mock_call:
            mock_call.return_value = {
                "summary": "Test analysis",
                "reasons": ["Strong fundamentals"],
                "action_plan": [{"title": "Hold", "description": "Maintain position"}],
                "analysis": "Detailed analysis",
            }

            service = LLMService()
            result = service.call_llm_api_json("Analyze AAPL stock")

            assert result["summary"] == "Test analysis"
            assert len(result["reasons"]) == 1
            assert len(result["action_plan"]) == 1
            mock_call.assert_called_once_with("Analyze AAPL stock")

    def test_llm_module_constants(self):
        """Test that LLM module constants are properly set."""
        import src.utils.llm as llm_module

        # Test that constants exist
        assert hasattr(llm_module, "LLM_API_ENDPOINT")
        assert hasattr(llm_module, "LLM_API_KEY")
        assert hasattr(llm_module, "LLM_MODEL")

        # Test that constants are strings
        assert isinstance(llm_module.LLM_API_ENDPOINT, str)
        assert isinstance(llm_module.LLM_API_KEY, str)
        assert isinstance(llm_module.LLM_MODEL, str)

    def test_llm_service_error_logging(self):
        """Test that LLM service properly logs errors."""
        from src.utils.llm import LLMService

        with patch("src.utils.llm.logger") as mock_logger:
            # Test JSON parsing error logging
            invalid_json = "not json"
            result = LLMService.ensure_json_format(invalid_json)

            assert result == {}
            mock_logger.warning.assert_called()
            mock_logger.error.assert_called()

    @patch("src.utils.llm.LLM_API_KEY", "test_key")
    @patch("src.utils.llm.LLM_API_ENDPOINT", "https://api.test.com/v1/chat/completions")
    @patch("src.utils.llm.OpenAI")
    def test_llm_service_endpoint_normalization(self, mock_openai):
        """Test that LLMService properly normalizes API endpoints."""
        from src.utils.llm import LLMService

        mock_client = Mock()
        mock_openai.return_value = mock_client

        service = LLMService()

        # Verify OpenAI client was created with normalized endpoint
        mock_openai.assert_called_once_with(api_key="test_key", base_url="https://api.test.com")  # Should be normalized

    def test_ensure_json_format_edge_cases(self):
        """Test ensure_json_format with various edge cases."""
        from src.utils.llm import LLMService

        # Test empty string
        result = LLMService.ensure_json_format("")
        assert result == {}

        # Test whitespace only
        result = LLMService.ensure_json_format("   \n\t   ")
        assert result == {}

        # Test JSON with nested objects
        nested_json = '{"outer": {"inner": {"value": 123}}, "array": [1, 2, {"nested": true}]}'
        result = LLMService.ensure_json_format(nested_json)
        expected = {"outer": {"inner": {"value": 123}}, "array": [1, 2, {"nested": True}]}
        assert result == expected

        # Test JSON with special characters
        special_json = '{"unicode": "测试", "escaped": "line\\nbreak", "quote": "He said \\"hello\\""}'
        result = LLMService.ensure_json_format(special_json)
        expected = {"unicode": "测试", "escaped": "line\nbreak", "quote": 'He said "hello"'}
        assert result == expected
