"""
Test suite for win rate calculations.

This module provides comprehensive tests for the new probability calculations
and enhanced scoring algorithms.
"""

import math
from unittest.mock import Mock, patch

import numpy as np
import pandas as pd
import pytest

from src.utils.options_probability import OptionsprobabilityCalculator
from src.utils.win_rate_scoring import WinRateScorer


@pytest.mark.unit
@pytest.mark.options
class TestOptionsProbabilityCalculator:
    """Test the enhanced probability calculation engine."""

    @pytest.fixture
    def calculator(self):
        """Create a probability calculator instance."""
        return OptionsprobabilityCalculator(risk_free_rate=0.05)

    def test_black_scholes_probability_puts(self, calculator):
        """Test Black-Scholes probability calculation for puts."""
        # Test case: OTM put (strike below current price)
        prob = calculator.black_scholes_probability(
            current_price=150.0, strike=140.0, time_to_expiry=30 / 365, volatility=0.25, option_type="put"
        )

        # OTM put should have lower probability of profit
        assert 0.0 <= prob <= 1.0
        assert prob < 0.5  # Should be less than 50% for OTM put

    def test_black_scholes_probability_calls(self, calculator):
        """Test Black-Scholes probability calculation for calls."""
        # Test case: OTM call (strike above current price)
        prob = calculator.black_scholes_probability(
            current_price=150.0, strike=160.0, time_to_expiry=30 / 365, volatility=0.25, option_type="call"
        )

        # OTM call should have lower probability of profit
        assert 0.0 <= prob <= 1.0
        assert prob < 0.5  # Should be less than 50% for OTM call

    def test_delta_based_probability_puts(self, calculator):
        """Test delta-based probability calculation for puts."""
        # Test with typical put delta
        prob = calculator.delta_based_probability(delta=-0.25, option_type="put")

        assert 0.0 <= prob <= 1.0
        assert prob == 0.75  # 1 + (-0.25) = 0.75

    def test_delta_based_probability_calls(self, calculator):
        """Test delta-based probability calculation for calls."""
        # Test with typical call delta
        prob = calculator.delta_based_probability(delta=0.30, option_type="call")

        assert 0.0 <= prob <= 1.0
        assert prob == 0.70  # 1 - 0.30 = 0.70

    def test_iron_condor_probability_advanced(self, calculator):
        """Test advanced iron condor probability calculation."""
        prob_results = calculator.iron_condor_probability_advanced(
            current_price=150.0,
            short_put_strike=140.0,
            short_call_strike=160.0,
            time_to_expiry=30 / 365,
            volatility=0.25,
            monte_carlo_sims=1000,
        )

        # Should return dictionary with multiple methods
        assert isinstance(prob_results, dict)
        assert "composite" in prob_results
        assert "black_scholes" in prob_results
        assert "normal_distribution" in prob_results

        # All probabilities should be valid
        for method, prob in prob_results.items():
            assert 0.0 <= prob <= 1.0

    def test_time_decay_adjustment(self, calculator):
        """Test time decay adjustment for different strategy types."""
        base_prob = 0.70

        # Test neutral strategy (benefits from time decay)
        adjusted_neutral = calculator.time_decay_adjustment(base_prob, days_to_expiry=30, strategy_type="neutral")
        assert adjusted_neutral >= base_prob  # Should increase

        # Test directional strategy (hurt by time decay)
        adjusted_directional = calculator.time_decay_adjustment(base_prob, days_to_expiry=30, strategy_type="directional")
        assert adjusted_directional <= base_prob  # Should decrease

    def test_market_regime_adjustment(self, calculator):
        """Test market regime adjustment for different volatility environments."""
        base_prob = 0.70

        # Test high volatility environment
        adjusted_high_vol = calculator.market_regime_adjustment(base_prob, implied_volatility=0.45, strategy_type="neutral")
        assert adjusted_high_vol >= base_prob  # Neutral strategies benefit from high vol

        # Test low volatility environment
        adjusted_low_vol = calculator.market_regime_adjustment(base_prob, implied_volatility=0.15, strategy_type="income")
        assert adjusted_low_vol <= base_prob  # Income strategies struggle in low vol

    def test_edge_cases(self, calculator):
        """Test edge cases and error handling."""
        # Test with zero time to expiry
        prob = calculator.black_scholes_probability(
            current_price=150.0, strike=140.0, time_to_expiry=0.0, volatility=0.25, option_type="put"
        )
        assert prob == 0.5  # Should return default

        # Test with zero volatility
        prob = calculator.black_scholes_probability(
            current_price=150.0, strike=140.0, time_to_expiry=30 / 365, volatility=0.0, option_type="put"
        )
        assert prob == 0.5  # Should return default


@pytest.mark.unit
@pytest.mark.options
class TestWinRateScorer:
    """Test the win rate scoring system."""

    @pytest.fixture
    def scorer(self):
        """Create a win rate scorer instance."""
        return WinRateScorer(risk_free_rate=0.05)

    @pytest.fixture
    def sample_put_data(self):
        """Create sample put options data for testing."""
        return pd.DataFrame(
            {
                "currentPrice": [150.0, 150.0, 150.0],
                "strike": [140.0, 145.0, 148.0],
                "dte": [30, 35, 40],
                "impliedVolatility": [0.25, 0.30, 0.35],
                "bid": [2.0, 3.0, 4.0],
                "ask": [2.2, 3.3, 4.4],
                "volume": [100, 150, 200],
                "openInterest": [500, 750, 1000],
                "delta": [-0.20, -0.25, -0.30],
                "bufferPercent": [0.067, 0.033, 0.013],
            }
        )

    @pytest.fixture
    def sample_iron_condor_data(self):
        """Create sample iron condor data for testing."""
        return pd.DataFrame(
            {
                "currentPrice": [150.0, 150.0],
                "shortPutStrike": [140.0, 135.0],
                "shortCallStrike": [160.0, 165.0],
                "dte": [30, 35],
                "impliedVolatility": [0.25, 0.30],
                "maxProfit": [200, 250],
                "maxLoss": [300, 350],
                "putWidth": [5, 5],
                "callWidth": [5, 5],
            }
        )

    def test_put_win_rate_scoring(self, scorer, sample_put_data):
        """Test win rate scoring for puts."""
        scores = scorer.calculate_put_win_rate_score(sample_put_data)

        # Should return valid scores
        assert len(scores) == len(sample_put_data)
        assert all(0 <= score <= 100 for score in scores)
        assert isinstance(scores, pd.Series)

        # Higher buffer should generally give higher scores (all else equal)
        # Note: This might not always be true due to other factors
        assert all(isinstance(score, (int, float)) for score in scores)

    def test_call_win_rate_scoring(self, scorer):
        """Test win rate scoring for calls."""
        sample_call_data = pd.DataFrame(
            {
                "currentPrice": [150.0, 150.0, 150.0],
                "strike": [155.0, 160.0, 165.0],
                "dte": [30, 35, 40],
                "impliedVolatility": [0.25, 0.30, 0.35],
                "bid": [2.0, 1.5, 1.0],
                "ask": [2.2, 1.7, 1.2],
                "volume": [100, 150, 200],
                "openInterest": [500, 750, 1000],
                "delta": [0.30, 0.25, 0.20],
                "upsideBufferPercent": [0.033, 0.067, 0.100],
            }
        )

        scores = scorer.calculate_call_win_rate_score(sample_call_data)

        # Should return valid scores
        assert len(scores) == len(sample_call_data)
        assert all(0 <= score <= 100 for score in scores)
        assert isinstance(scores, pd.Series)

    def test_iron_condor_win_rate_scoring(self, scorer, sample_iron_condor_data):
        """Test win rate scoring for iron condors."""
        scores = scorer.calculate_iron_condor_win_rate_score(sample_iron_condor_data)

        # Should return valid scores
        assert len(scores) == len(sample_iron_condor_data)
        assert all(0 <= score <= 100 for score in scores)
        assert isinstance(scores, pd.Series)

    def test_market_conditions_integration(self, scorer, sample_put_data):
        """Test integration with market conditions."""
        # Test with high volatility market conditions
        high_vol_conditions = {"volatility_regime": "high_vol", "avg_iv": 0.45, "market_stress": 0.7}

        scores_high_vol = scorer.calculate_put_win_rate_score(sample_put_data, high_vol_conditions)

        # Test with low volatility market conditions
        low_vol_conditions = {"volatility_regime": "low_vol", "avg_iv": 0.15, "market_stress": 0.2}

        scores_low_vol = scorer.calculate_put_win_rate_score(sample_put_data, low_vol_conditions)

        # Both should return valid scores
        assert all(0 <= score <= 100 for score in scores_high_vol)
        assert all(0 <= score <= 100 for score in scores_low_vol)

    def test_error_handling(self, scorer):
        """Test error handling with invalid data."""
        # Test with empty DataFrame
        empty_df = pd.DataFrame()
        scores = scorer.calculate_put_win_rate_score(empty_df)
        assert len(scores) == 0

        # Test with missing required columns
        invalid_df = pd.DataFrame({"invalid_column": [1, 2, 3]})
        scores = scorer.calculate_put_win_rate_score(invalid_df)
        assert len(scores) == len(invalid_df)
        assert all(score == 0.0 for score in scores)  # Should return default scores

    def test_probability_weighting(self, scorer, sample_put_data):
        """Test that probability is properly weighted in scoring."""
        # Modify data to have very high probability
        high_prob_data = sample_put_data.copy()
        high_prob_data["delta"] = [-0.10, -0.10, -0.10]  # High probability puts

        # Modify data to have very low probability
        low_prob_data = sample_put_data.copy()
        low_prob_data["delta"] = [-0.45, -0.45, -0.45]  # Low probability puts

        high_prob_scores = scorer.calculate_put_win_rate_score(high_prob_data)
        low_prob_scores = scorer.calculate_put_win_rate_score(low_prob_data)

        # High probability options should generally score higher
        # (though other factors may influence this)
        assert all(isinstance(score, (int, float)) for score in high_prob_scores)
        assert all(isinstance(score, (int, float)) for score in low_prob_scores)
