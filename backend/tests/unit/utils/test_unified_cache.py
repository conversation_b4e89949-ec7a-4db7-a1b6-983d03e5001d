"""
Test unified stock cache functionality
"""

import asyncio
import tempfile
import time
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, Mock, patch

import pandas as pd
import pytest

from src.utils.cache import (
    CacheConfig,
    DataType,
    L1MemoryCache,
    StockCache,
    stock_cache,
)


class TestL1MemoryCache:
    """Test L1 memory cache functionality"""

    def setup_method(self):
        """Setup test environment"""
        self.cache = L1MemoryCache(max_size=3, default_ttl=1)

    def test_basic_operations(self):
        """Test basic cache operations"""
        # Test set and get
        self.cache.set("key1", "value1")
        assert self.cache.get("key1") == "value1"

        # Test non-existent key
        assert self.cache.get("nonexistent") is None

    def test_ttl_expiration(self):
        """Test TTL expiration"""
        self.cache.set("key1", "value1", ttl=1)
        assert self.cache.get("key1") == "value1"

        # Wait for expiration
        time.sleep(1.1)
        assert self.cache.get("key1") is None

    def test_lru_eviction(self):
        """Test LRU eviction"""
        # Fill cache to capacity
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")

        # Add one more item (should evict key1)
        self.cache.set("key4", "value4")

        assert self.cache.get("key1") is None
        assert self.cache.get("key2") == "value2"
        assert self.cache.get("key3") == "value3"
        assert self.cache.get("key4") == "value4"


class TestCacheConfig:
    """Test cache configuration"""

    def test_default_config(self):
        """Test default configuration values"""
        config = CacheConfig()
        assert config.price_ttl == 300
        assert config.historical_ttl == 3600
        assert config.batch_size == 50
        assert config.l1_max_size == 1000

    def test_custom_config(self):
        """Test custom configuration"""
        config = CacheConfig(price_ttl=600, historical_ttl=7200, batch_size=100, l1_max_size=2000)
        assert config.price_ttl == 600
        assert config.historical_ttl == 7200
        assert config.batch_size == 100
        assert config.l1_max_size == 2000


class TestUnifiedStockCache:
    """Test unified stock cache functionality"""

    def setup_method(self):
        """Setup test environment"""
        self.config = CacheConfig(redis_url="redis://localhost:6379/15")  # Use test database
        self.cache = StockCache(config=self.config)

    def teardown_method(self):
        """Cleanup after tests"""
        try:
            self.cache.clear_cache()
        except:
            pass

    def test_cache_key_generation(self):
        """Test cache key generation"""
        key = self.cache._get_cache_key(DataType.PRICE, "AAPL")
        assert key == "stock_cache:price:AAPL"

        key_with_extra = self.cache._get_cache_key(DataType.HISTORICAL, "GOOGL", "1d")
        assert key_with_extra == "stock_cache:historical:GOOGL:1d"

    def test_ttl_for_type(self):
        """Test TTL mapping for different data types"""
        assert self.cache._get_ttl_for_type(DataType.PRICE) == self.config.price_ttl
        assert self.cache._get_ttl_for_type(DataType.HISTORICAL) == self.config.historical_ttl

    def test_backward_compatibility_methods(self):
        """Test backward compatibility methods exist"""
        # Test that all original methods exist
        assert hasattr(self.cache, "get_historical_data")
        assert hasattr(self.cache, "get_current_prices")
        assert hasattr(self.cache, "clear_cache")
        assert hasattr(self.cache, "update_price")
        assert hasattr(self.cache, "is_cache_valid")

    def test_historical_data_method(self):
        """Test historical data retrieval"""
        with patch("yfinance.download") as mock_download:
            mock_data = pd.DataFrame(
                {
                    "Open": [100, 101, 102],
                    "High": [105, 106, 107],
                    "Low": [95, 96, 97],
                    "Close": [100, 101, 102],
                    "Volume": [1000, 1100, 1200],
                }
            )
            mock_download.return_value = mock_data

            df = self.cache.get_historical_data("AAPL")
            assert isinstance(df, pd.DataFrame)
            assert not df.empty

    def test_current_prices_method(self):
        """Test current prices retrieval"""
        with patch.object(self.cache, "_fetch_prices_batch") as mock_fetch:
            mock_fetch.return_value = {"AAPL": 150.0, "GOOGL": 2500.0}

            prices = self.cache.get_current_prices(["AAPL", "GOOGL"])
            assert isinstance(prices, dict)
            assert "AAPL" in prices
            assert "GOOGL" in prices

    def test_portfolio_methods(self):
        """Test portfolio-specific methods"""
        symbols = ["AAPL", "GOOGL", "MSFT"]

        with patch.object(self.cache, "_fetch_prices_batch") as mock_fetch:
            mock_fetch.return_value = {"AAPL": 150.0, "GOOGL": 2500.0, "MSFT": 300.0}

            # Test portfolio prices
            prices = self.cache.get_portfolio_prices(symbols)
            assert isinstance(prices, dict)
            assert len(prices) <= len(symbols)

    def test_batch_optimization_methods(self):
        """Test new batch optimization methods"""
        symbols = ["AAPL", "GOOGL", "MSFT"]

        # Test optimized batch data method
        with patch.object(self.cache, "_get_current_prices_sync") as mock_prices:
            with patch.object(self.cache, "get_historical_data_batch") as mock_historical:
                mock_prices.return_value = {"AAPL": 150.0, "GOOGL": 2500.0, "MSFT": 300.0}
                mock_historical.return_value = {
                    "AAPL": pd.DataFrame({"Close": [100, 110, 120]}),
                    "GOOGL": pd.DataFrame({"Close": [2400, 2450, 2500]}),
                    "MSFT": pd.DataFrame({"Close": [290, 295, 300]}),
                }

                batch_data = self.cache.get_batch_data_optimized(symbols)
                assert "prices" in batch_data
                assert "historical" in batch_data
                assert len(batch_data["prices"]) == 3
                assert len(batch_data["historical"]) == 3

        # Test cache warming
        with patch.object(self.cache, "_get_current_prices_sync") as mock_prices:
            with patch.object(self.cache, "get_historical_data_batch") as mock_historical:
                mock_prices.return_value = {"AAPL": 150.0, "GOOGL": 2500.0}
                mock_historical.return_value = {"AAPL": pd.DataFrame({"Close": [100, 110, 120]})}

                results = self.cache.warm_cache_for_symbols(symbols)
                assert isinstance(results, dict)
                assert len(results) == len(symbols)


class TestGlobalInstances:
    """Test global cache instances"""

    def test_global_instances(self):
        """Test global cache instances"""
        assert stock_cache is not None
        assert stock_cache is not None
        assert isinstance(stock_cache, StockCache)
        assert isinstance(stock_cache, StockCache)
        assert stock_cache is stock_cache  # Should be the same instance


class TestErrorHandling:
    """Test error handling and fallback mechanisms"""

    def setup_method(self):
        """Setup test environment"""
        self.config = CacheConfig(redis_url="redis://localhost:6379/15")
        self.cache = StockCache(config=self.config)

    def test_empty_symbol_handling(self):
        """Test handling of empty or invalid symbols"""
        with patch("yfinance.download") as mock_download:
            # Mock empty response for invalid symbols
            mock_download.return_value = pd.DataFrame()

            # Test empty symbol
            df = self.cache.get_historical_data("")
            assert isinstance(df, pd.DataFrame)
            assert df.empty

        # Test None symbol handling
        prices = self.cache.get_current_prices([])
        assert isinstance(prices, dict)
        # Redis cache may return some default data, so just check it's a dict

    def test_api_failure_handling(self):
        """Test API failure handling"""
        # Clear cache first to ensure we hit the API
        self.cache.clear_cache()

        with patch("yfinance.Ticker") as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.history.side_effect = Exception("API Error")
            mock_ticker.return_value = mock_ticker_instance

            # Use a unique symbol to avoid any cached data
            df = self.cache.get_historical_data("TESTFAIL123")
            assert isinstance(df, pd.DataFrame)
            assert df.empty  # Should return empty DataFrame on error

    def test_cache_clear_safety(self):
        """Test cache clearing is safe"""
        # Should not raise exceptions
        self.cache.clear_cache()
        # clear_cache no longer takes arguments


class TestL1CacheOperations:
    """Test L1 cache specific operations"""

    def setup_method(self):
        """Setup for each test"""
        self.cache = StockCache(cache_dir=None, cache_expiry=3600)

    def teardown_method(self):
        """Cleanup after each test"""
        self.cache.clear_cache()
        self.cache.shutdown()

    def test_l1_cache_delete_existing_key(self):
        """Test L1 cache delete with existing key"""
        # Add data to L1 cache
        self.cache.l1_cache.set("test_key", "test_value", 3600)

        # Delete existing key
        result = self.cache.l1_cache.delete("test_key")
        assert result is True

        # Verify key is deleted
        assert self.cache.l1_cache.get("test_key") is None

    def test_l1_cache_delete_nonexistent_key(self):
        """Test L1 cache delete with non-existent key"""
        result = self.cache.l1_cache.delete("nonexistent_key")
        assert result is False

    def test_l1_cache_size(self):
        """Test L1 cache size method"""
        # Initially empty
        assert self.cache.l1_cache.size() == 0

        # Add some items
        self.cache.l1_cache.set("key1", "value1", 3600)
        self.cache.l1_cache.set("key2", "value2", 3600)

        # Check size
        assert self.cache.l1_cache.size() == 2


class TestRedisErrorHandling:
    """Test Redis error handling scenarios"""

    def setup_method(self):
        """Setup for each test"""
        self.cache = StockCache(cache_dir=None, cache_expiry=3600)

    def teardown_method(self):
        """Cleanup after each test"""
        self.cache.clear_cache()
        self.cache.shutdown()

    def test_redis_connection_health_check(self):
        """Test Redis connection health check"""
        # Test that we can check Redis health status
        initial_health = self.cache.redis_healthy
        assert isinstance(initial_health, bool)

    def test_redis_set_error_handling(self):
        """Test Redis set operation error handling"""
        # Mock Redis client to raise exception on set
        with patch.object(self.cache, "redis_client") as mock_redis:
            mock_redis.setex.side_effect = Exception("Redis set failed")
            self.cache.redis_healthy = True

            # This should handle the error gracefully
            from src.utils.cache import DataType

            self.cache.set_multi_level("test_key", "test_value", DataType.PRICE)

            # Should still work with L1 cache
            assert self.cache.l1_cache.get("test_key") is not None

    def test_redis_delete_error_handling(self):
        """Test Redis delete operation error handling"""
        # Mock Redis client to raise exception on delete
        with patch.object(self.cache, "redis_client") as mock_redis:
            mock_redis.delete.side_effect = Exception("Redis delete failed")
            self.cache.redis_healthy = True

            # This should handle the error gracefully
            self.cache.delete_multi_level("test_key")

            # Should not raise exception
            assert True  # Test passes if no exception is raised


class TestCacheValidation:
    """Test cache validation methods"""

    def setup_method(self):
        """Setup for each test"""
        self.cache = StockCache(cache_dir=None, cache_expiry=3600)

    def teardown_method(self):
        """Cleanup after each test"""
        self.cache.clear_cache()
        self.cache.shutdown()

    def test_is_cache_valid_with_valid_data(self):
        """Test cache validation with valid data"""
        # Cache some price data using update_price method
        self.cache.update_price("AAPL", 150.0)

        # Check if cache is valid
        assert self.cache.is_cache_valid("AAPL", "price") is True

    def test_is_cache_valid_with_invalid_data_type(self):
        """Test cache validation with invalid data type"""
        result = self.cache.is_cache_valid("AAPL", "invalid_type")
        assert result is False

    def test_is_cache_valid_with_no_cached_data(self):
        """Test cache validation with no cached data"""
        result = self.cache.is_cache_valid("NONEXISTENT", "price")
        assert result is False


class TestMultiLevelCacheOperations:
    """Test multi-level cache operations"""

    def setup_method(self):
        """Setup for each test"""
        self.cache = StockCache(cache_dir=None, cache_expiry=3600)

    def teardown_method(self):
        """Cleanup after each test"""
        self.cache.clear_cache()
        self.cache.shutdown()

    def test_delete_multi_level_l1_only(self):
        """Test multi-level delete with L1 cache only"""
        # Add data to L1 cache
        self.cache.l1_cache.set("test_key", "test_value", 3600)

        # Delete from multi-level cache
        self.cache.delete_multi_level("test_key")

        # Verify deletion
        assert self.cache.l1_cache.get("test_key") is None

    def test_delete_multi_level_with_redis_healthy(self):
        """Test multi-level delete with Redis healthy"""
        # Mock Redis client
        with patch.object(self.cache, "redis_client") as mock_redis:
            self.cache.redis_healthy = True

            # Delete from multi-level cache
            self.cache.delete_multi_level("test_key")

            # Verify Redis delete was called
            mock_redis.delete.assert_called_once_with("test_key")


class TestCircuitBreakerAndHealthChecks:
    """Test circuit breaker and health check functionality"""

    def setup_method(self):
        """Setup for each test"""
        self.cache = StockCache(cache_dir=None, cache_expiry=3600)

    def teardown_method(self):
        """Cleanup after each test"""
        self.cache.clear_cache()
        self.cache.shutdown()

    def test_handle_redis_error_sets_unhealthy(self):
        """Test that Redis errors set Redis as unhealthy"""
        # Initially healthy
        self.cache.redis_healthy = True

        # Trigger Redis error handling multiple times to exceed threshold
        for _ in range(10):  # Exceed any reasonable threshold
            self.cache._handle_redis_error()

        # Should set Redis as unhealthy
        assert self.cache.redis_healthy is False

    def test_redis_health_status_property(self):
        """Test Redis health status property"""
        # Test when Redis is healthy
        self.cache.redis_healthy = True
        assert self.cache.redis_healthy is True

        # Test when Redis is unhealthy
        self.cache.redis_healthy = False
        assert self.cache.redis_healthy is False


class TestCacheErrorPaths:
    """Test error handling paths in cache operations"""

    def setup_method(self):
        """Setup for each test"""
        self.cache = StockCache(cache_dir=None, cache_expiry=3600)

    def teardown_method(self):
        """Cleanup after each test"""
        self.cache.clear_cache()
        self.cache.shutdown()

    def test_clear_cache_with_redis_error(self):
        """Test cache clearing with Redis error"""
        # Mock Redis client to raise exception
        with patch.object(self.cache, "redis_client") as mock_redis:
            mock_redis.flushdb.side_effect = Exception("Redis flush failed")
            self.cache.redis_healthy = True

            # Should handle error gracefully
            self.cache.clear_cache()

            # Should not raise exception
            assert True

    def test_clear_cache_general_error(self):
        """Test cache clearing with general error"""
        # Mock L1 cache to raise exception
        with patch.object(self.cache.l1_cache, "clear") as mock_clear:
            mock_clear.side_effect = Exception("L1 cache clear failed")

            # Should handle error gracefully
            self.cache.clear_cache()

            # Should not raise exception
            assert True

    def test_shutdown_with_redis_error(self):
        """Test shutdown with Redis error"""
        # Mock Redis client to raise exception on quit
        with patch.object(self.cache, "redis_client") as mock_redis:
            mock_redis.quit.side_effect = Exception("Redis quit failed")
            self.cache.redis_healthy = True

            # Should handle error gracefully
            self.cache.shutdown()

            # Should not raise exception
            assert True

    def test_shutdown_general_error(self):
        """Test shutdown with general error"""
        # Mock to raise exception during shutdown
        with patch.object(self.cache, "redis_client", None):
            # Force an error by making redis_client None but redis_healthy True
            self.cache.redis_healthy = True

            # Should handle error gracefully
            self.cache.shutdown()

            # Should not raise exception
            assert True


class TestDataTypeValidation:
    """Test data type validation and enum handling"""

    def setup_method(self):
        """Setup for each test"""
        self.cache = StockCache(cache_dir=None, cache_expiry=3600)

    def teardown_method(self):
        """Cleanup after each test"""
        self.cache.clear_cache()
        self.cache.shutdown()

    def test_cache_key_generation_for_different_types(self):
        """Test cache key generation for different data types"""
        from src.utils.cache import DataType

        # Test different data types
        price_key = self.cache._get_cache_key(DataType.PRICE, "AAPL")
        historical_key = self.cache._get_cache_key(DataType.HISTORICAL, "AAPL")

        assert "price" in price_key
        assert "historical" in historical_key
        assert price_key != historical_key

    def test_get_ttl_for_different_types(self):
        """Test TTL for different data types"""
        from src.utils.cache import DataType

        # Test different data types
        price_ttl = self.cache._get_ttl_for_type(DataType.PRICE)
        historical_ttl = self.cache._get_ttl_for_type(DataType.HISTORICAL)

        assert isinstance(price_ttl, int)
        assert isinstance(historical_ttl, int)
        assert price_ttl > 0
        assert historical_ttl > 0
