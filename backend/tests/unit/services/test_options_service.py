"""
Test cases for options service functionality.
"""

import json
import os
import sqlite3
import tempfile
from datetime import datetime
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import numpy as np
import pandas as pd
import pytest

from src.services.options_service import OptionsAnalysisService, convert_timestamps_to_strings, get_db_connection


class TestConvertTimestampsToStrings:
    """Test cases for timestamp conversion utility."""

    def test_convert_pandas_timestamp(self):
        """Test conversion of pandas Timestamp."""
        timestamp = pd.Timestamp("2024-01-01 12:00:00")
        result = convert_timestamps_to_strings(timestamp)
        assert isinstance(result, str)
        assert "2024-01-01T12:00:00" in result

    def test_convert_datetime(self):
        """Test conversion of datetime object."""
        dt = datetime(2024, 1, 1, 12, 0, 0)
        result = convert_timestamps_to_strings(dt)
        assert isinstance(result, str)
        assert "2024-01-01T12:00:00" in result

    def test_convert_dict_with_timestamps(self):
        """Test conversion of dictionary containing timestamps."""
        data = {"timestamp": pd.Timestamp("2024-01-01"), "value": 100, "nested": {"date": datetime(2024, 1, 1)}}
        result = convert_timestamps_to_strings(data)

        assert isinstance(result["timestamp"], str)
        assert isinstance(result["nested"]["date"], str)
        assert result["value"] == 100

    def test_convert_list_with_timestamps(self):
        """Test conversion of list containing timestamps."""
        data = [pd.Timestamp("2024-01-01"), 100, "text"]
        result = convert_timestamps_to_strings(data)

        assert isinstance(result[0], str)
        assert result[1] == 100
        assert result[2] == "text"

    def test_convert_numpy_types(self):
        """Test conversion of numpy data types."""
        data = {"int64": np.int64(42), "float64": np.float64(3.14), "bool": np.bool_(True)}
        result = convert_timestamps_to_strings(data)

        assert isinstance(result["int64"], int)
        assert isinstance(result["float64"], float)
        assert isinstance(result["bool"], bool)

    def test_convert_nan_values(self):
        """Test conversion of NaN values."""
        data = {"nan_value": np.nan, "inf_value": np.inf, "neg_inf": -np.inf}
        result = convert_timestamps_to_strings(data)

        # NaN values should be converted to None, but inf values remain as inf
        assert result["nan_value"] is None
        # Note: inf values are not converted to None in the actual implementation
        assert np.isinf(result["inf_value"])
        assert np.isinf(result["neg_inf"])

    def test_convert_tuple(self):
        """Test conversion of tuple containing timestamps."""
        data = (pd.Timestamp("2024-01-01"), 100, "text")
        result = convert_timestamps_to_strings(data)

        assert isinstance(result, tuple)
        assert isinstance(result[0], str)
        assert result[1] == 100

    def test_convert_nested_structure(self):
        """Test conversion of deeply nested structure."""
        data = {"level1": {"level2": [{"timestamp": pd.Timestamp("2024-01-01")}, {"value": np.int64(42)}]}}
        result = convert_timestamps_to_strings(data)

        assert isinstance(result["level1"]["level2"][0]["timestamp"], str)
        assert isinstance(result["level1"]["level2"][1]["value"], int)


class TestGetDbConnection:
    """Test cases for database connection utility."""

    @patch("sqlite3.connect")
    def test_get_db_connection_success(self, mock_connect):
        """Test successful database connection."""
        mock_conn = Mock()
        mock_connect.return_value = mock_conn

        result = get_db_connection()

        assert result == mock_conn
        assert mock_conn.row_factory == sqlite3.Row

    @patch("sqlite3.connect")
    def test_get_db_connection_path(self, mock_connect):
        """Test database connection uses correct path."""
        mock_conn = Mock()
        mock_connect.return_value = mock_conn

        get_db_connection()

        # Verify the path contains the expected database file
        call_args = mock_connect.call_args[0][0]
        assert "stock_trading.db" in call_args


class TestOptionsAnalysisService:
    """Test cases for OptionsAnalysisService."""

    def setup_method(self):
        """Setup test fixtures."""
        self.service = OptionsAnalysisService()

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.OptionsDataManager")
    def test_initialization(self, mock_data_manager, mock_stock_cache):
        """Test service initialization."""
        service = OptionsAnalysisService()

        assert service.stock_cache is not None
        assert service.logger is not None
        assert service.options_data_manager is not None

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_get_user_watchlist_success(self, mock_get_conn, mock_cache):
        """Test successful user watchlist retrieval."""
        mock_conn = Mock()
        mock_conn.execute.return_value.fetchone.return_value = [
            1,
            "Test Watchlist",
            '["AAPL", "MSFT"]',
            "2024-01-01T12:00:00",
            "2024-01-01T12:00:00",
        ]
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.get.return_value = None  # Cache miss
        mock_redis_client.setex.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        result = service.get_user_watchlist(account_id=1, watchlist_id=1)

        assert result["watchlist_id"] == 1
        assert result["name"] == "Test Watchlist"
        assert result["symbols"] == ["AAPL", "MSFT"]

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_get_user_watchlist_not_found(self, mock_get_conn, mock_cache):
        """Test user watchlist retrieval when not found."""
        mock_conn = Mock()
        mock_conn.execute.return_value.fetchone.return_value = None
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.get.return_value = None  # Cache miss
        mock_redis_client.setex.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        result = service.get_user_watchlist(account_id=1, watchlist_id=999)

        # The actual implementation returns None when not found
        assert result is None

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_get_user_watchlist_all(self, mock_get_conn, mock_cache):
        """Test getting all user watchlists."""
        mock_conn = Mock()
        mock_conn.execute.return_value.fetchall.return_value = [
            [1, "Watchlist 1", '["AAPL"]', "2024-01-01T12:00:00", "2024-01-01T12:00:00"],
            [2, "Watchlist 2", '["MSFT"]', "2024-01-01T12:00:00", "2024-01-01T12:00:00"],
        ]
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.get.return_value = None  # Cache miss
        mock_redis_client.setex.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        result = service.get_user_watchlist(account_id=1)

        assert len(result) == 2
        assert result[0]["name"] == "Watchlist 1"
        assert result[1]["name"] == "Watchlist 2"

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_create_or_update_watchlist_create(self, mock_get_conn, mock_cache):
        """Test successful watchlist creation."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.lastrowid = 123
        mock_cursor.rowcount = 0  # Simulate new creation
        mock_conn.execute.return_value = mock_cursor
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.delete.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        result = service.create_or_update_watchlist(account_id=1, name="Test Watchlist", symbols=["AAPL", "MSFT"])

        assert result == 123
        mock_conn.execute.assert_called()
        mock_conn.commit.assert_called()
        mock_redis_client.delete.assert_called()

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_create_or_update_watchlist_update(self, mock_get_conn, mock_cache):
        """Test successful watchlist update."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.lastrowid = 456
        mock_cursor.rowcount = 1  # Mock successful update
        mock_conn.execute.return_value = mock_cursor
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.delete.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        result = service.create_or_update_watchlist(
            account_id=1,
            watchlist_id=1,
            name="Updated Watchlist",
            symbols=["AAPL", "GOOGL"],
        )

        # When updating, it returns the watchlist_id, not lastrowid
        assert result == 1
        mock_conn.execute.assert_called()
        mock_conn.commit.assert_called()
        mock_redis_client.delete.assert_called()

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_delete_watchlist_success(self, mock_get_conn, mock_cache):
        """Test successful watchlist deletion."""
        mock_conn = Mock()

        # Setup for account_id query
        mock_account_cursor = Mock()
        mock_account_cursor.fetchone.return_value = [1]  # account_id

        # Setup for delete query
        mock_delete_cursor = Mock()
        mock_delete_cursor.rowcount = 1

        # Configure execute to return different cursors for different calls
        mock_conn.execute.side_effect = [mock_account_cursor, mock_delete_cursor]
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.delete.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        result = service.delete_watchlist(watchlist_id=1)

        assert result is True
        assert mock_conn.execute.call_count == 2
        mock_conn.commit.assert_called_once()
        mock_redis_client.delete.assert_called()

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_get_strategy_config_existing(self, mock_get_conn, mock_cache):
        """Test getting existing strategy configuration."""
        mock_conn = Mock()
        config_data = {"min_dte": 30, "max_dte": 45}
        mock_conn.execute.return_value.fetchone.return_value = [json.dumps(config_data)]
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.get.return_value = None  # Cache miss
        mock_redis_client.setex.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        result = service.get_strategy_config(account_id=1, strategy_type="cash_secured_puts")

        assert result == config_data

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_get_strategy_config_default(self, mock_get_conn, mock_cache):
        """Test getting default strategy configuration."""
        mock_conn = Mock()
        mock_conn.execute.return_value.fetchone.return_value = None
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.get.return_value = None  # Cache miss
        mock_redis_client.setex.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        with patch.object(service, "_get_current_market_volatility", return_value=0.20):
            result = service.get_strategy_config(account_id=1, strategy_type="cash_secured_puts")

        assert isinstance(result, dict)
        assert "min_dte" in result
        assert "max_dte" in result

    @patch("src.services.options_service.stock_cache")
    @patch("src.services.options_service.get_db_connection")
    def test_save_strategy_config_success(self, mock_get_conn, mock_cache):
        """Test successful strategy configuration save."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.lastrowid = 456
        mock_conn.execute.return_value = mock_cursor
        mock_get_conn.return_value = mock_conn

        # Set up mock cache with proper structure
        mock_redis_client = Mock()
        mock_redis_client.delete.return_value = True
        mock_cache.redis_client = mock_redis_client

        # Create service after mocks are set up
        service = OptionsAnalysisService()
        config_data = {"min_dte": 30, "max_dte": 45}
        result = service.save_strategy_config(account_id=1, strategy_type="cash_secured_puts", config_data=config_data)

        assert result == 456
        mock_conn.execute.assert_called()
        mock_conn.commit.assert_called()
        mock_redis_client.delete.assert_called()

    @patch("src.utils.cache.get_vix_ticker")
    def test_get_current_market_volatility_success(self, mock_get_vix):
        """Test successful market volatility retrieval."""
        mock_ticker = Mock()
        vix_data = pd.DataFrame({"Close": [20.0, 22.0, 21.0, 23.0, 22.5]}, index=pd.date_range("2024-01-01", periods=5))
        mock_ticker.history.return_value = vix_data
        mock_get_vix.return_value = mock_ticker

        result = self.service._get_current_market_volatility()

        assert isinstance(result, float)
        assert result > 0

    @patch("src.utils.cache.get_vix_ticker")
    def test_get_current_market_volatility_error(self, mock_get_vix):
        """Test market volatility retrieval with error."""
        mock_get_vix.side_effect = Exception("API Error")

        result = self.service._get_current_market_volatility()

        # The actual default value may vary, just check it's a reasonable float
        assert isinstance(result, float)
        assert 0.1 <= result <= 0.3  # Reasonable volatility range

    def test_get_default_strategy_config_cash_secured_puts(self):
        """Test default configuration for cash secured puts."""
        with patch.object(self.service, "_get_current_market_volatility", return_value=0.20):
            config = self.service._get_default_strategy_config("cash_secured_puts")

        assert isinstance(config, dict)
        assert config["min_dte"] == 30
        assert config["max_dte"] == 45
        assert "min_annual_roi" in config
        assert "max_delta" in config

    def test_get_default_strategy_config_covered_calls(self):
        """Test default configuration for covered calls."""
        with patch.object(self.service, "_get_current_market_volatility", return_value=0.20):
            config = self.service._get_default_strategy_config("covered_calls")

        assert isinstance(config, dict)
        assert config["min_dte"] == 25
        assert config["max_dte"] == 35
        assert "min_annual_roi" in config
        assert "max_delta" in config

    def test_get_default_strategy_config_iron_condors(self):
        """Test default configuration for iron condors."""
        with patch.object(self.service, "_get_current_market_volatility", return_value=0.20):
            config = self.service._get_default_strategy_config("iron_condors")

        assert isinstance(config, dict)
        assert config["min_dte"] == 25
        assert config["max_dte"] == 35
        assert "target_delta_short_put" in config
        assert "target_delta_short_call" in config

    def test_get_default_strategy_config_invalid_type(self):
        """Test default configuration for invalid strategy type."""
        with patch.object(self.service, "_get_current_market_volatility", return_value=0.20):
            config = self.service._get_default_strategy_config("invalid_strategy")

        assert config == {}

    def test_apply_market_condition_adjustments_low_volatility(self):
        """Test market condition adjustments for low volatility."""
        base_config = {"min_annual_roi": 0.10, "min_buffer_percent": 0.05}

        result = self.service._apply_market_condition_adjustments(
            base_config, "cash_secured_puts", 0.10  # Very low volatility to trigger adjustment
        )

        # Low volatility should increase ROI requirements
        assert result["min_annual_roi"] > base_config["min_annual_roi"]

    def test_apply_market_condition_adjustments_high_volatility(self):
        """Test market condition adjustments for high volatility."""
        base_config = {"min_annual_roi": 0.10, "min_buffer_percent": 0.05}

        result = self.service._apply_market_condition_adjustments(base_config, "cash_secured_puts", 0.35)

        # High volatility should decrease ROI requirements and increase buffers
        assert result["min_annual_roi"] < base_config["min_annual_roi"]
        assert result["min_buffer_percent"] > base_config["min_buffer_percent"]
