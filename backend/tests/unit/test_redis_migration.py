"""
测试Redis迁移后的功能
验证所有cached_prices表的使用都已替换为Redis缓存
"""

import os
import sqlite3
import tempfile
from unittest.mock import Mock, patch

import pytest

from src.api.analytics import get_holdings, get_overview
from src.utils.cache import get_stock_cache
from src.utils.calculations import calculate_current_market_value, get_top_stocks
from tests.utils.test_helpers import DatabaseTestHelper


class TestRedisMigration:
    """测试Redis迁移后的功能"""

    @pytest.fixture
    def temp_db(self):
        """创建临时数据库（不包含cached_prices表）"""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)

        # 创建新的schema（不包含cached_prices表）
        conn = sqlite3.connect(path)
        conn.execute(
            """
            CREATE TABLE accounts (
                account_id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        )

        conn.execute(
            """
            CREATE TABLE transactions (
                transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER NOT NULL,
                symbol TEXT NOT NULL,
                quantity REAL NOT NULL,
                price REAL NOT NULL,
                trans_time TIMESTAMP NOT NULL,
                FOREIGN KEY (account_id) REFERENCES accounts(account_id)
            )
        """
        )

        conn.execute(
            """
            CREATE TABLE daily_returns (
                account_id INTEGER NOT NULL,
                date DATE NOT NULL,
                return_rate REAL NOT NULL,
                PRIMARY KEY (account_id, date),
                FOREIGN KEY (account_id) REFERENCES accounts(account_id)
            )
        """
        )

        conn.execute(
            """
            CREATE TABLE realized_gains (
                account_id INTEGER NOT NULL,
                symbol TEXT NOT NULL,
                buy_date DATE NOT NULL,
                sell_date DATE NOT NULL,
                buy_quantity REAL NOT NULL,
                buy_price REAL NOT NULL,
                sell_price REAL NOT NULL,
                realized_gain REAL NOT NULL,
                FOREIGN KEY (account_id) REFERENCES accounts(account_id)
            )
        """
        )

        conn.commit()
        conn.close()

        yield path
        os.unlink(path)

    def test_cache_sync_without_cached_prices_table(self, temp_db):
        """测试统一缓存同步功能（Redis-only）"""
        stock_cache = get_stock_cache()

        # 测试统一缓存同步，不再支持数据库缓存
        result = stock_cache.unified_cache_sync()

        # 数据库同步应该返回空结果，因为不再支持
        assert result["database_sync"]["synced"] == 0
        assert result["database_sync"]["skipped"] == 0
        assert result["database_sync"]["errors"] == 0

    @patch("src.utils.calculations.get_stock_cache")
    def test_calculate_current_market_value_uses_redis(self, mock_cache, temp_db):
        """测试calculate_current_market_value使用Redis缓存"""
        # 设置测试数据
        conn = sqlite3.connect(temp_db)
        conn.execute("INSERT INTO accounts (account_id, name) VALUES (1, 'Test Account')")
        conn.execute(
            """
            INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) 
            VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')
        """
        )
        conn.commit()

        # Mock Redis缓存
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 160.0}

        # 调用函数
        current_value, total_cost, return_rate = calculate_current_market_value(conn, account_id=1)

        # 验证Redis缓存被调用
        mock_cache_instance.get_portfolio_prices.assert_called_once()

        # 验证计算结果
        assert current_value == 16000.0  # 100 * 160
        assert total_cost > 0  # 应该有成本

        conn.close()

    @patch("src.utils.calculations.get_stock_cache")
    def test_get_top_stocks_uses_redis(self, mock_cache, temp_db):
        """测试get_top_stocks使用Redis缓存"""
        # 设置测试数据
        conn = sqlite3.connect(temp_db)
        conn.execute("INSERT INTO accounts (account_id, name) VALUES (1, 'Test Account')")
        conn.execute(
            """
            INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) 
            VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')
        """
        )
        conn.execute(
            """
            INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) 
            VALUES (1, 'GOOGL', 50, 2000.0, '2023-01-01')
        """
        )
        conn.commit()

        # Mock Redis缓存
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 160.0, "GOOGL": 2200.0}

        # 调用函数
        top_stocks = get_top_stocks(conn, account_id=1, limit=5)

        # 验证Redis缓存被调用
        mock_cache_instance.get_portfolio_prices.assert_called_once()

        # 验证结果
        assert len(top_stocks) == 2
        assert all("symbol" in stock for stock in top_stocks)

        conn.close()

    def test_no_cached_prices_table_exists(self, temp_db):
        """验证cached_prices表不存在"""
        conn = sqlite3.connect(temp_db)
        cursor = conn.cursor()

        # 检查cached_prices表是否存在
        cursor.execute(
            """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='cached_prices'
        """
        )

        result = cursor.fetchone()
        assert result is None, "cached_prices表不应该存在"

        conn.close()

    def test_no_cached_prices_index_exists(self, temp_db):
        """验证cached_prices相关索引不存在"""
        conn = sqlite3.connect(temp_db)
        cursor = conn.cursor()

        # 检查idx_prices_symbol_date索引是否存在
        cursor.execute(
            """
            SELECT name FROM sqlite_master 
            WHERE type='index' AND name='idx_prices_symbol_date'
        """
        )

        result = cursor.fetchone()
        assert result is None, "idx_prices_symbol_date索引不应该存在"

        conn.close()

    @patch("src.api.analytics.get_stock_cache")
    def test_analytics_api_uses_redis(self, mock_cache, temp_db):
        """测试analytics API使用Redis缓存"""
        from flask import Flask

        from src.api.analytics import analytics_bp

        app = Flask(__name__)
        app.register_blueprint(analytics_bp, url_prefix="/api/analytics")

        # Mock Redis缓存
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 160.0}

        with app.test_client() as client:
            with patch("src.api.analytics.get_db_connection") as mock_db:
                mock_conn = Mock()
                mock_db.return_value = mock_conn
                mock_conn.execute.return_value.fetchall.return_value = []

                # 测试holdings端点
                response = client.get("/api/analytics/holdings")
                assert response.status_code == 200

    def test_redis_cache_health_check(self):
        """测试Redis缓存健康检查"""
        stock_cache = get_stock_cache()

        # 获取缓存健康状态
        health = stock_cache.get_cache_health()

        # 验证健康检查包含必要信息
        assert "redis_healthy" in health
        assert "l1_cache_size" in health
        assert "timestamp" in health

    @patch("src.utils.cache.get_stock_cache")
    def test_portfolio_prices_batch_fetch(self, mock_cache):
        """测试批量获取投资组合价格"""
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.get_portfolio_prices.return_value = {"AAPL": 160.0, "GOOGL": 2200.0, "MSFT": 300.0}

        symbols = ["AAPL", "GOOGL", "MSFT"]
        stock_cache = get_stock_cache()
        prices = stock_cache.get_portfolio_prices(symbols)

        # 验证批量获取功能 (Redis缓存返回真实价格，证明缓存正常工作)
        assert len(prices) >= 1  # 至少返回一些价格数据
        assert "AAPL" in prices  # AAPL应该有价格数据
        assert isinstance(prices["AAPL"], (int, float))  # 价格应该是数字

    def test_migration_completed(self):
        """验证迁移已完成，不再需要迁移脚本"""
        import os

        script_path = os.path.join(os.path.dirname(__file__), "../../scripts/migrate_remove_cached_prices.py")
        assert not os.path.exists(script_path), "迁移脚本应该已被移除，因为迁移已完成"

    def test_schema_updated(self):
        """验证schema.sql已更新"""
        import os

        schema_path = os.path.join(os.path.dirname(__file__), "../../config/schema.sql")

        with open(schema_path, "r") as f:
            schema_content = f.read()

        # 验证cached_prices表定义已移除
        assert "CREATE TABLE IF NOT EXISTS cached_prices" not in schema_content
        assert "idx_prices_symbol_date" not in schema_content or "removed" in schema_content.lower()
