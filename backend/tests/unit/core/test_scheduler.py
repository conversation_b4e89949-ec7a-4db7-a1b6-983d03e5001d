"""
Unit tests for the scheduler module.
"""

import os
import sqlite3
import tempfile
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import MagicMock, Mock, call, patch

import pandas as pd
import pytest


class TestStockDataManager:
    """Test cases for StockDataManager class."""

    @pytest.fixture(autouse=True)
    def reset_singleton(self):
        """Reset the StockDataManager singleton before each test."""
        from src.core.scheduler import StockDataManager

        StockDataManager._instance = None
        yield
        # Clean up after test
        if StockDataManager._instance:
            if hasattr(StockDataManager._instance, "stop_scheduler"):
                StockDataManager._instance.stop_scheduler()
            StockDataManager._instance = None

    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)

        # Create basic schema
        conn = sqlite3.connect(path)
        conn.execute(
            """
            CREATE TABLE transactions (
                transaction_id INTEGER PRIMARY KEY,
                account_id INTEGER,
                symbol TEXT,
                quantity REAL,
                price REAL,
                trans_time TEXT
            )
        """
        )
        conn.execute(
            """
            CREATE TABLE cached_prices (
                symbol TEXT,
                date TEXT,
                close REAL,
                PRIMARY KEY (symbol, date)
            )
        """
        )
        conn.commit()
        conn.close()

        yield path
        os.unlink(path)

    @pytest.fixture
    def temp_cache_dir(self):
        """Create a temporary cache directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        import shutil

        shutil.rmtree(temp_dir, ignore_errors=True)

    def test_stock_data_manager_singleton(self, temp_db, temp_cache_dir):
        """Test that StockDataManager is a singleton."""
        from src.core.scheduler import StockDataManager

        manager1 = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        manager2 = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        assert manager1 is manager2

    def test_stock_data_manager_initialization(self, temp_db, temp_cache_dir):
        """Test StockDataManager initialization."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        assert manager.db_path == temp_db
        assert manager.cache_dir == Path(temp_cache_dir)
        assert manager.cache_dir.exists()
        assert manager.stop_flag is not None
        assert manager.initialized is True
        assert manager.logger is not None
        assert manager.model_retrainer is not None

    def test_get_db_connection(self, temp_db, temp_cache_dir):
        """Test database connection."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()

        assert conn is not None
        assert isinstance(conn, sqlite3.Connection)

        # Test that we can execute queries
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        assert "transactions" in tables
        assert "cached_prices" in tables

        conn.close()

    def test_get_all_tickers(self, temp_db, temp_cache_dir):
        """Test getting all tickers from database."""
        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'GOOGL', 50, 2500.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 50, 155.0, '2023-01-02')"
        )
        conn.commit()
        conn.close()

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()
        tickers = manager.get_all_tickers(conn)
        conn.close()

        assert set(tickers) == {"AAPL", "GOOGL"}

    def test_should_update_cache_no_file(self, temp_db, temp_cache_dir):
        """Test should_update_cache when cache file doesn't exist."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Cache file doesn't exist
        should_update = manager.should_update_cache("AAPL")
        assert should_update is True

    def test_should_update_cache_old_file(self, temp_db, temp_cache_dir):
        """Test should_update_cache when cache file is old."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create an old cache file
        cache_file = manager.cache_dir / "AAPL_data.parquet"
        cache_file.touch()

        # Set modification time to 2 days ago
        old_time = time.time() - (2 * 24 * 60 * 60)
        os.utime(cache_file, (old_time, old_time))

        should_update = manager.should_update_cache("AAPL")
        assert should_update is True

    def test_should_update_cache_recent_file(self, temp_db, temp_cache_dir):
        """Test should_update_cache when cache file is recent."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create a recent cache file
        cache_file = manager.cache_dir / "AAPL_data.parquet"
        cache_file.touch()

        should_update = manager.should_update_cache("AAPL")
        assert should_update is False

    def test_redis_cache_integration(self, temp_db, temp_cache_dir):
        """Test Redis cache integration functionality."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test that unified cache is accessible
        assert manager.unified_cache is not None

        # Test cache health
        health = manager.unified_cache.get_cache_health()
        assert "redis_healthy" in health
        assert "l1_cache_size" in health
        assert "timestamp" in health

    def test_cache_sync_functionality(self, temp_db, temp_cache_dir):
        """Test cache synchronization functionality (Redis-only)."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test unified cache sync (database cache is no longer supported)
        result = manager.unified_cache.unified_cache_sync(cache_dir=temp_cache_dir)

        # Database sync should return empty results since it's no longer supported
        assert result["database_sync"]["synced"] == 0
        assert result["database_sync"]["errors"] == 0

    def test_check_cache_health_basic(self, temp_db, temp_cache_dir):
        """Test cache health check functionality."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Should not raise exception
        manager.check_cache_health()

    def test_unified_cache_sync_on_startup(self, temp_db, temp_cache_dir):
        """Test that unified cache sync runs on startup."""
        from src.core.scheduler import StockDataManager

        # Creating manager should trigger cache sync
        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Verify cache system is initialized (Redis may or may not be available)
        cache_health = manager.unified_cache.get_cache_health()

        # Check that the cache health response has the expected structure
        assert "redis_healthy" in cache_health
        assert "l1_cache_size" in cache_health
        assert "timestamp" in cache_health
        assert "stats" in cache_health

        # L1 cache should always be enabled
        stats = cache_health["stats"]
        assert stats["l1_cache_enabled"] is True

        # Redis health depends on environment - in CI it may be False, locally it may be True
        # The important thing is that the system handles both cases gracefully
        assert isinstance(cache_health["redis_healthy"], bool)
        assert isinstance(stats["redis_enabled"], bool)

        # Cache size should be a non-negative integer
        assert isinstance(cache_health["l1_cache_size"], int)
        assert cache_health["l1_cache_size"] >= 0

    def test_start_stop_scheduler(self, temp_db, temp_cache_dir):
        """Test starting and stopping the scheduler."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Start scheduler
        manager.start_scheduler()
        assert manager.scheduler_thread is not None
        assert manager.scheduler_thread.is_alive()

        # Stop scheduler
        manager.stop_scheduler()

        # Give it a moment to stop
        time.sleep(0.1)
        assert manager.stop_flag.is_set()

    @patch("src.core.scheduler.schedule")
    def test_run_scheduler_jobs(self, mock_schedule, temp_db, temp_cache_dir):
        """Test that scheduler sets up jobs correctly."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Mock the schedule module
        mock_schedule.every.return_value.hours.do = Mock()
        mock_schedule.every.return_value.day.at.return_value.do = Mock()
        mock_schedule.run_pending = Mock()

        # Start scheduler in a separate thread and stop it quickly
        manager.start_scheduler()
        time.sleep(0.1)  # Let it set up jobs
        manager.stop_scheduler()

        # Verify jobs were scheduled
        assert mock_schedule.every.called

    def test_run_job_in_thread(self, temp_db, temp_cache_dir):
        """Test running a job in a separate thread."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create a test function that sets a flag
        test_flag = threading.Event()

        def test_job():
            test_flag.set()

        # Run the job
        manager._run_job_in_thread(test_job)

        # Wait for the job to complete
        test_flag.wait(timeout=1.0)
        assert test_flag.is_set()


class TestModelRetrainingScheduler:
    """Test cases for ModelRetrainingScheduler class."""

    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)

        # Create basic schema
        conn = sqlite3.connect(path)
        conn.execute(
            """
            CREATE TABLE transactions (
                transaction_id INTEGER PRIMARY KEY,
                account_id INTEGER,
                symbol TEXT,
                quantity REAL,
                price REAL,
                trans_time TEXT
            )
        """
        )
        conn.commit()
        conn.close()

        yield path
        os.unlink(path)

    def test_model_retraining_scheduler_initialization(self, temp_db):
        """Test ModelRetrainingScheduler initialization."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)

        assert scheduler.db_path == temp_db
        assert scheduler.logger is not None

    def test_get_db_connection(self, temp_db):
        """Test database connection."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        conn = scheduler.get_db_connection()

        assert conn is not None
        assert isinstance(conn, sqlite3.Connection)
        conn.close()

    def test_get_active_position_tickers_empty(self, temp_db):
        """Test getting active position tickers when none exist."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        tickers = scheduler.get_active_position_tickers()

        assert tickers == []

    def test_get_active_position_tickers_with_data(self, temp_db):
        """Test getting active position tickers with data."""
        from src.core.scheduler import ModelRetrainingScheduler

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'GOOGL', 50, 2500.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', -50, 155.0, '2023-01-02')"
        )  # Partial sell
        conn.commit()
        conn.close()

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        tickers = scheduler.get_active_position_tickers()

        # Should return tickers with net positive positions
        assert set(tickers) == {"AAPL", "GOOGL"}

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_no_tickers(self, mock_pipeline, temp_db):
        """Test daily model retraining job when no active tickers."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        scheduler.run_daily_model_retraining_job()

        # Should not call the training pipeline
        mock_pipeline.assert_not_called()

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_with_tickers(self, mock_pipeline, temp_db):
        """Test daily model retraining job with active tickers."""
        from src.core.scheduler import ModelRetrainingScheduler

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        scheduler.run_daily_model_retraining_job()

        # Should call the training pipeline with active tickers
        mock_pipeline.assert_called_once_with(ticker_symbols_to_process=["AAPL"], force_retrain=True)

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_with_provided_tickers(self, mock_pipeline, temp_db):
        """Test daily model retraining job with provided ticker list."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        test_tickers = ["AAPL", "GOOGL", "MSFT"]
        scheduler.run_daily_model_retraining_job(ticker_symbols_to_process=test_tickers)

        # Should call the training pipeline with provided tickers
        mock_pipeline.assert_called_once_with(ticker_symbols_to_process=test_tickers, force_retrain=True)

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_error_handling(self, mock_pipeline, temp_db):
        """Test daily model retraining job error handling."""
        from src.core.scheduler import ModelRetrainingScheduler

        # Mock pipeline to raise exception
        mock_pipeline.side_effect = Exception("Training failed")

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)

        # Should not raise exception, just log it
        scheduler.run_daily_model_retraining_job()

        mock_pipeline.assert_called_once()


class TestSchedulerUtilities:
    """Test cases for scheduler utility functions."""

    @patch("src.core.scheduler.get_calendar")
    def test_is_market_open_today_open(self, mock_get_calendar):
        """Test is_market_open_today when market is open."""
        from src.core.scheduler import is_market_open_today

        # Mock calendar
        mock_calendar = Mock()
        mock_get_calendar.return_value = mock_calendar
        mock_calendar.is_session.return_value = True

        result = is_market_open_today()

        assert result is True
        mock_get_calendar.assert_called_once_with("NYSE")
        mock_calendar.is_session.assert_called_once()

    @patch("src.core.scheduler.get_calendar")
    def test_is_market_open_today_closed(self, mock_get_calendar):
        """Test is_market_open_today when market is closed."""
        from src.core.scheduler import is_market_open_today

        # Mock calendar
        mock_calendar = Mock()
        mock_get_calendar.return_value = mock_calendar
        mock_calendar.is_session.return_value = False

        result = is_market_open_today()

        assert result is False

    def test_cache_health_monitoring(self):
        """Test cache health monitoring functionality."""
        import os
        import tempfile

        from src.core.scheduler import StockDataManager

        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")
            cache_dir = os.path.join(temp_dir, "cache")
            os.makedirs(cache_dir, exist_ok=True)

            manager = StockDataManager(db_path=db_path, cache_dir=cache_dir)

            # Test cache health check
            manager.check_cache_health()

            # Test cache health info
            health = manager.unified_cache.get_cache_health()
            assert "redis_healthy" in health
            assert "l1_cache_size" in health
            assert "timestamp" in health

    def test_start_stop_scheduler_functions(self):
        """Test module-level start_scheduler and stop_scheduler functions."""
        from src.core.scheduler import start_scheduler, stock_data_manager, stop_scheduler

        with (
            patch.object(stock_data_manager, "start_scheduler") as mock_start,
            patch.object(stock_data_manager, "stop_scheduler") as mock_stop,
        ):

            start_scheduler()
            mock_start.assert_called_once()

            stop_scheduler()
            mock_stop.assert_called_once()


class TestStockDataManagerAdvanced:
    """Advanced test cases for StockDataManager class."""

    @pytest.fixture(autouse=True)
    def reset_singleton(self):
        """Reset the StockDataManager singleton before each test."""
        from src.core.scheduler import StockDataManager

        if StockDataManager._instance is not None:
            if hasattr(StockDataManager._instance, "stop_scheduler"):
                StockDataManager._instance.stop_scheduler()
            StockDataManager._instance = None
        yield
        if StockDataManager._instance is not None:
            if hasattr(StockDataManager._instance, "stop_scheduler"):
                StockDataManager._instance.stop_scheduler()
            StockDataManager._instance = None

    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)

        # Create basic schema
        conn = sqlite3.connect(path)
        conn.execute(
            """
            CREATE TABLE transactions (
                transaction_id INTEGER PRIMARY KEY,
                account_id INTEGER,
                symbol TEXT,
                quantity REAL,
                price REAL,
                trans_time TEXT
            )
        """
        )
        conn.execute(
            """
            CREATE TABLE cached_prices (
                symbol TEXT,
                date TEXT,
                close REAL,
                PRIMARY KEY (symbol, date)
            )
        """
        )
        conn.commit()
        conn.close()

        yield path
        os.unlink(path)

    @pytest.fixture
    def temp_cache_dir(self):
        """Create a temporary cache directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        import shutil

        shutil.rmtree(temp_dir, ignore_errors=True)

    def test_cache_health_check_detailed(self, temp_db, temp_cache_dir):
        """Test detailed cache health check functionality."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Get cache health info
        health = manager.unified_cache.get_cache_health()

        # Verify health info structure
        assert "redis_healthy" in health
        assert "l1_cache_size" in health
        assert "timestamp" in health
        assert "stats" in health

        # Verify stats structure
        stats = health["stats"]
        assert "l1_cache_enabled" in stats
        assert "redis_enabled" in stats
        assert "compression_enabled" in stats

    def test_unified_cache_integration(self, temp_db, temp_cache_dir):
        """Test unified cache integration with scheduler."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test that unified cache is accessible
        assert manager.unified_cache is not None

        # Test cache sync functionality
        result = manager.unified_cache.unified_cache_sync(cache_dir=temp_cache_dir)

        assert "success" in result
        assert "total_synced" in result
        assert "total_errors" in result

    def test_scheduler_singleton_pattern(self, temp_db, temp_cache_dir):
        """Test that scheduler follows singleton pattern."""
        from src.core.scheduler import StockDataManager

        # Create two instances
        manager1 = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        manager2 = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Should be the same instance
        assert manager1 is manager2

    def test_cache_sync_with_database_data(self, temp_db, temp_cache_dir):
        """Test cache sync with Redis-only cache system."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test unified cache sync (database cache is no longer supported)
        result = manager.unified_cache.unified_cache_sync(cache_dir=temp_cache_dir)

        # Database sync should return empty results since it's no longer supported
        assert result["database_sync"]["synced"] == 0
        assert result["database_sync"]["errors"] == 0

    def test_get_all_tickers_functionality(self, temp_db, temp_cache_dir):
        """Test get_all_tickers method still works correctly."""
        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'GOOGL', 50, 2500.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'CASH', 1000, 1.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        tickers = manager.get_all_tickers()

        # Should return stock symbols but not CASH
        assert set(tickers) == {"AAPL", "GOOGL"}
        assert "CASH" not in tickers

    def test_scheduler_thread_lifecycle(self, temp_db, temp_cache_dir):
        """Test complete scheduler thread lifecycle."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Initially no scheduler thread
        assert manager.scheduler_thread is None

        # Start scheduler
        manager.start_scheduler()
        assert manager.scheduler_thread is not None
        assert manager.scheduler_thread.is_alive()
        assert not manager.stop_flag.is_set()

        # Stop scheduler
        manager.stop_scheduler()

        # Wait for thread to stop
        manager.scheduler_thread.join(timeout=1.0)
        assert manager.stop_flag.is_set()
        assert not manager.scheduler_thread.is_alive()

    def test_scheduler_already_running(self, temp_db, temp_cache_dir):
        """Test starting scheduler when already running."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Start scheduler
        manager.start_scheduler()
        first_thread = manager.scheduler_thread

        # Try to start again
        manager.start_scheduler()

        # Should be the same thread
        assert manager.scheduler_thread is first_thread

        # Clean up
        manager.stop_scheduler()
        manager.scheduler_thread.join(timeout=1.0)

    @patch("src.core.scheduler.schedule.run_pending")
    def test_scheduler_error_handling(self, mock_run_pending, temp_db, temp_cache_dir):
        """Test scheduler error handling during execution."""
        from src.core.scheduler import StockDataManager

        # Mock schedule.run_pending to raise exception
        mock_run_pending.side_effect = Exception("Scheduler error")

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Start scheduler
        manager.start_scheduler()

        # Let it run briefly to encounter the error
        time.sleep(0.2)

        # Scheduler should still be running (error handling should keep it alive)
        assert manager.scheduler_thread.is_alive()

        # Stop scheduler
        manager.stop_scheduler()
        manager.scheduler_thread.join(timeout=5.0)

        # Now it should have stopped
        assert not manager.scheduler_thread.is_alive()

    def test_threading_safety(self, temp_db, temp_cache_dir):
        """Test thread safety of singleton pattern."""
        import concurrent.futures

        from src.core.scheduler import StockDataManager

        instances = []

        def create_instance():
            return StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create multiple instances concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_instance) for _ in range(10)]
            instances = [future.result() for future in futures]

        # All instances should be the same object
        first_instance = instances[0]
        for instance in instances[1:]:
            assert instance is first_instance

    def test_scheduler_job_execution(self, temp_db, temp_cache_dir):
        """Test scheduler job execution functionality."""
        from unittest.mock import patch

        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test job execution
        with patch.object(manager, "check_cache_health") as mock_health:
            manager._run_job_in_thread(manager.check_cache_health)
            # Give some time for the thread to execute
            import time

            time.sleep(0.1)
            mock_health.assert_called_once()

    def test_cache_directory_creation(self):
        """Test cache directory creation."""
        import os
        import tempfile

        from src.core.scheduler import StockDataManager

        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")
            cache_dir = os.path.join(temp_dir, "nonexistent_cache")

            # Cache directory should be created automatically
            StockDataManager(db_path=db_path, cache_dir=cache_dir)
            assert os.path.exists(cache_dir)

    def test_get_latest_db_price(self, temp_db, temp_cache_dir):
        """Test get_latest_db_price functionality."""
        import sqlite3

        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute("INSERT INTO cached_prices (symbol, date, close) VALUES ('AAPL', '2023-01-01', 150.0)")
        conn.execute("INSERT INTO cached_prices (symbol, date, close) VALUES ('AAPL', '2023-01-02', 155.0)")
        conn.commit()
        conn.close()

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()

        date, price = manager.get_latest_db_price("AAPL", conn)

        assert date == "2023-01-02"
        assert price == 155.0

        conn.close()

    def test_should_update_cache_edge_cases(self, temp_db, temp_cache_dir):
        """Test should_update_cache edge cases."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test with non-existent file
        should_update = manager.should_update_cache("NONEXISTENT")
        assert should_update is True

    def test_unified_cache_error_handling(self, temp_db, temp_cache_dir):
        """Test unified cache error handling."""
        from unittest.mock import patch

        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test error handling in unified cache sync
        with patch.object(manager.unified_cache, "unified_cache_sync", side_effect=Exception("Cache Error")):
            # Should not raise exception
            try:
                manager.unified_cache.unified_cache_sync(cache_dir=temp_cache_dir)
            except Exception:
                pass  # Expected to handle gracefully

    def test_scheduler_initialization_components(self, temp_db, temp_cache_dir):
        """Test scheduler initialization components."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Test that all components are initialized
        assert manager.db_path == temp_db
        assert str(manager.cache_dir) == temp_cache_dir
        assert manager.unified_cache is not None
        assert manager.logger is not None

    def test_get_all_tickers_with_cash_filtering(self, temp_db, temp_cache_dir):
        """Test get_all_tickers filters out CASH properly."""
        import sqlite3

        from src.core.scheduler import StockDataManager

        # Add test data including CASH
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'CASH', 1000, 1.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'GOOGL', 50, 2500.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        tickers = manager.get_all_tickers()

        # Should return stock symbols but not CASH
        assert set(tickers) == {"AAPL", "GOOGL"}
        assert "CASH" not in tickers
