-- Accounts table
CREATE TABLE IF NOT EXISTS accounts (
    account_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    symbol TEXT NOT NULL,
    quantity REAL NOT NULL,
    price REAL NOT NULL,
    trans_time TIMESTAMP NOT NULL,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);



-- Daily returns table for time series data
CREATE TABLE IF NOT EXISTS daily_returns (
    account_id INTEGER NOT NULL,
    date DATE NOT NULL,
    return_rate REAL NOT NULL,
    PRIMARY KEY (account_id, date),
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- Portfolio snapshots table
CREATE TABLE IF NOT EXISTS portfolio_snapshots (
    account_id INTEGER NOT NULL,
    date DATE NOT NULL,
    initial_capital REAL NOT NULL,
    current_value REAL NOT NULL,
    unrealized_gains REAL NOT NULL,
    realized_gains REAL NOT NULL,
    return_rate REAL NOT NULL,
    PRIMARY KEY (account_id, date),
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- Realized gains table
CREATE TABLE IF NOT EXISTS realized_gains (
    gain_id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    symbol TEXT NOT NULL,
    buy_date TIMESTAMP NOT NULL,
    sell_date TIMESTAMP NOT NULL,
    buy_quantity REAL NOT NULL,
    buy_price REAL NOT NULL,
    sell_price REAL NOT NULL,
    realized_gain REAL NOT NULL,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- Account summary materialized view
CREATE VIEW IF NOT EXISTS account_summary AS
SELECT 
    a.account_id,
    a.name,
    SUM(t.quantity * (p.close - t.price)) AS unrealized_gains,
    (SELECT COALESCE(SUM(realized_gain), 0) FROM realized_gains WHERE account_id = a.account_id) AS realized_gains,
    SUM(t.quantity * p.close) as current_value,
    (SELECT COALESCE(SUM(realized_gain), 0) FROM realized_gains WHERE account_id = a.account_id) +
    SUM(t.quantity * (p.close - t.price)) AS total_gains,
    CASE 
        WHEN SUM(CASE WHEN t.quantity > 0 THEN t.quantity * t.price ELSE 0 END) > 0 
        THEN ((SELECT COALESCE(SUM(realized_gain), 0) FROM realized_gains WHERE account_id = a.account_id) +
              SUM(t.quantity * (p.close - t.price))) / 
             SUM(CASE WHEN t.quantity > 0 THEN t.quantity * t.price ELSE 0 END)
        ELSE 0 
    END AS return_rate,
    COUNT(DISTINCT t.symbol) as total_symbols
FROM accounts a
LEFT JOIN transactions t ON a.account_id = t.account_id

GROUP BY a.account_id, a.name;

-- User watchlists for options analysis
CREATE TABLE IF NOT EXISTS user_watchlists (
    watchlist_id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    name TEXT NOT NULL DEFAULT 'Default Watchlist',
    symbols TEXT NOT NULL, -- JSON array of symbols
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- Options strategy configurations
CREATE TABLE IF NOT EXISTS strategy_configs (
    config_id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    strategy_type TEXT NOT NULL, -- 'cash_secured_puts', 'covered_calls', 'iron_condors', 'wheel'
    config_data TEXT NOT NULL, -- JSON configuration parameters
    is_default BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions(account_id);
CREATE INDEX IF NOT EXISTS idx_transactions_symbol ON transactions(symbol);
CREATE INDEX IF NOT EXISTS idx_transactions_time ON transactions(trans_time);

CREATE INDEX IF NOT EXISTS idx_daily_returns_account ON daily_returns(account_id);
CREATE INDEX IF NOT EXISTS idx_daily_returns_date ON daily_returns(date);
CREATE INDEX IF NOT EXISTS idx_realized_gains_account ON realized_gains(account_id);
CREATE INDEX IF NOT EXISTS idx_realized_gains_symbol ON realized_gains(symbol);
CREATE INDEX IF NOT EXISTS idx_realized_gains_dates ON realized_gains(buy_date, sell_date);

-- Options-related indexes
CREATE INDEX IF NOT EXISTS idx_watchlists_account ON user_watchlists(account_id);
CREATE INDEX IF NOT EXISTS idx_strategy_configs_account ON strategy_configs(account_id);
CREATE INDEX IF NOT EXISTS idx_strategy_configs_type ON strategy_configs(strategy_type);
CREATE INDEX IF NOT EXISTS idx_portfolio_snapshots_account ON portfolio_snapshots(account_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_snapshots_date ON portfolio_snapshots(date); 