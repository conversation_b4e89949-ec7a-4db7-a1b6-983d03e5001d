import threading
import uuid
from collections import defaultdict
from datetime import datetime

from flask import jsonify, request

from ..core.scheduler import stock_data_manager
from ..utils.cache import stock_cache
from ..utils.calculations import calculate_realized_gains
from ..utils.file_processor import process_row, read_csv_chunks, validate_columns
from ..utils.logger import get_logger
from . import get_db_connection, transactions_bp

# 存储导入任务的状态
import_tasks = {}
# 存储每个账户的导入历史
account_import_history = {}
# 存储每个账户的临时交易记录
account_pending_transactions = {}
# 存储CUSIP到Ticker的映射关系
symbol_mappings = {}
rsu_symbols = set()
logger = get_logger()


class ImportTask:
    def __init__(self, account_id, is_last_file):
        self.id = str(uuid.uuid4())
        self.account_id = account_id
        self.is_last_file = is_last_file
        self.status = "processing"  # processing, completed, failed
        self.stage = "parsing"  # parsing, validating, importing
        self.progress = 0
        self.error = None
        self.success_count = 0
        self.failed_records = []
        self.start_date = None  # 记录最早的交易日期
        self.transactions = []  # 存储解析出的交易记录
        self.rsu_vesting_events = []  # 存储 RSU 归属事件
        self.rsu_sell_transactions = {}  # 存储 RSU 相关的卖出交易
        self.split_transactions = []  # 存储拆分交易
        self.split_transactions_by_symbol = {}  # 存储拆分交易按symbol分组


def process_batch_import_task(task_id, files_content, account_id):
    task = import_tasks[task_id]
    conn = None
    try:
        account_id = int(account_id)
        logger.debug(f"Converted account_id to integer: {account_id}")

        # 1. Parse CSV files
        task.stage = "parsing"
        task.progress = 0

        components_dict = {"regular": [], "split": [], "rsu": []}
        total_files = len(files_content)
        rsu_symbols = set()  # Track RSU symbols for quick lookup

        for file_index, content in enumerate(files_content):
            try:
                chunks = read_csv_chunks(content)
                first_chunk = next(chunks)
                required_columns = ["Run Date", "Symbol", "Quantity", "Price", "Action"]
                validate_columns(first_chunk, required_columns)

                chunks = read_csv_chunks(content)  # Reset for full processing
                for chunk in chunks:
                    for index, row in chunk.iterrows():
                        result, error, rsu_vesting_event, rsu_sell_info = process_row(row, account_id)

                        if isinstance(result, dict):
                            result["file_index"] = file_index + 1
                            result["row"] = index + 2
                            if result.get("type") == "REVERSE_SPLIT_COMPONENT":
                                components_dict["split"].append(result)
                            else:
                                components_dict["regular"].append(result)

                        if error:
                            error["file"] = file_index + 1
                            task.failed_records.append(error)

                        if rsu_vesting_event:
                            rsu_vesting_event["file_index"] = file_index + 1
                            components_dict["rsu"].append(rsu_vesting_event)
                            task.rsu_vesting_events.append(rsu_vesting_event)
                            rsu_symbols.add(rsu_vesting_event["symbol"])  # Track RSU symbol

                        if rsu_sell_info:
                            rsu_sell_info["file_index"] = file_index + 1
                            rsu_sell_info["row"] = index + 2
                            key = f"{rsu_sell_info['symbol']}_{rsu_sell_info['trans_time']}"
                            if key not in task.rsu_sell_transactions:
                                task.rsu_sell_transactions[key] = []
                            task.rsu_sell_transactions[key].append(rsu_sell_info)

                task.progress = ((file_index + 1) / total_files) * 30
            except Exception as e:
                task.failed_records.append({"file": file_index + 1, "row": 0, "error": "文件解析失败", "reason": str(e)})
                logger.error(f"Error parsing file {file_index + 1}: {str(e)}", exc_info=True)

        if not any(components_dict.values()):
            task.status = "completed" if not task.failed_records else "failed"
            task.progress = 100
            logger.info(
                "Import completed: No transactions found." if not task.failed_records else "Import failed: No valid data."
            )
            return

        task.start_date = min((t["trans_time"] for comps in components_dict.values() for t in comps), default=None)
        task.stage = "processing"
        task.progress = 30

        # 2. Process transactions
        conn = get_db_connection()
        all_transactions = parallel_process_transactions(task_id, components_dict, account_id, conn)
        if not all_transactions:
            task.status = "completed" if not task.failed_records else "failed"
            task.progress = 100
            return

        # --- BEGIN RSU Netting Pre-processing ---
        # This section aims to adjust RSU vesting cost basis for shares sold on the same day.
        processed_transactions_for_netting = []
        original_rsu_vesting_txns = [t for t in all_transactions if t.get("transaction_type") == "RSU_VESTING"]
        other_txns_including_sells = [t for t in all_transactions if t.get("transaction_type") != "RSU_VESTING"]

        # Create a way to track usage of sell_info from task.rsu_sell_transactions
        # This helps if a single parsed sell_info could be hypothetically matched multiple times.
        # We will mark sell_info dicts directly in task.rsu_sell_transactions for simplicity here.
        # Ensure each sell_info dict has a '_netted_qty_from_this_sell_info' field.
        for key in task.rsu_sell_transactions:
            for sell_info_dict in task.rsu_sell_transactions[key]:
                sell_info_dict.setdefault("_netted_qty_from_this_sell_info", 0.0)

        for vest_txn in original_rsu_vesting_txns:
            symbol = vest_txn["symbol"]
            vest_date = vest_txn["trans_time"]
            total_vest_qty = vest_txn["quantity"]  # Positive
            original_vest_price = vest_txn["price"]  # Typically 0 - this is the basis for sold shares
            account_id_for_vest = vest_txn["account_id"]  # Capture account_id from vest_txn

            unique_sell_info_tuples_used_for_this_vest = set()
            market_price_for_kept_shares = original_vest_price  # Default, updated by first sell
            shares_sold_this_vest_event = 0.0
            total_proceeds_from_rsu_sells = 0.0
            sell_transactions_to_skip_db_insert = []

            lookup_key = f"{symbol}_{vest_date}"
            if lookup_key in task.rsu_sell_transactions:
                for sell_info_from_parser in task.rsu_sell_transactions[lookup_key]:
                    current_sell_info_tuple = (
                        sell_info_from_parser.get("symbol"),
                        sell_info_from_parser.get("trans_time"),
                        sell_info_from_parser.get("quantity"),
                        round(sell_info_from_parser.get("price", 0.0), 6),
                        sell_info_from_parser.get("file_index"),
                        sell_info_from_parser.get("row"),
                    )
                    if current_sell_info_tuple in unique_sell_info_tuples_used_for_this_vest:
                        continue

                    parsed_sell_qty_abs = abs(sell_info_from_parser["quantity"])
                    # available_to_net_from_this_sell_info tracks how much of THIS sell_info_from_parser is available.
                    # We assume each sell_info_from_parser corresponds to a unique sell event from the file.
                    available_to_net_from_this_sell_info = parsed_sell_qty_abs
                    # No, _netted_qty_from_this_sell_info was per vest. We need simpler logic if a sell_info is unique.
                    # We are processing ONE vest_txn here. A sell_info should only be used once against this vest_txn.

                    if available_to_net_from_this_sell_info > 0:
                        matched_sell_txn_object = None
                        for sell_candidate_txn in other_txns_including_sells:
                            # Check if this sell_candidate_txn has already been fully consumed by *any* RSU netting
                            if sell_candidate_txn.get("_is_rsu_netted_and_skipped", False):
                                continue
                            if (
                                sell_candidate_txn["symbol"] == sell_info_from_parser["symbol"]
                                and sell_candidate_txn["trans_time"] == sell_info_from_parser["trans_time"]
                                and sell_candidate_txn["quantity"] == sell_info_from_parser["quantity"]  # Quantity is negative
                                and round(sell_candidate_txn["price"], 6) == round(sell_info_from_parser["price"], 6)
                                and sell_candidate_txn.get("file_index") == sell_info_from_parser.get("file_index")
                                and sell_candidate_txn.get("row") == sell_info_from_parser.get("row")
                            ):
                                matched_sell_txn_object = sell_candidate_txn
                                break

                        if matched_sell_txn_object:
                            if shares_sold_this_vest_event == 0:  # First sell associated with this vest
                                market_price_for_kept_shares = matched_sell_txn_object["price"]

                            # Quantity of this specific sell event from file
                            qty_of_this_specific_sell = abs(matched_sell_txn_object["quantity"])

                            shares_sold_this_vest_event += qty_of_this_specific_sell
                            total_proceeds_from_rsu_sells += qty_of_this_specific_sell * matched_sell_txn_object["price"]

                            unique_sell_info_tuples_used_for_this_vest.add(current_sell_info_tuple)
                            # Mark the matched_sell_txn_object to be skipped for DB insert later
                            matched_sell_txn_object["_is_rsu_netted_and_skipped"] = True
                            sell_transactions_to_skip_db_insert.append(matched_sell_txn_object)
                            logger.info(
                                f"RSU Logic: Sell {matched_sell_txn_object['symbol']} {-qty_of_this_specific_sell} @ {matched_sell_txn_object['price']} on {matched_sell_txn_object['trans_time']} will be used for gain calc and skipped for DB insert."
                            )

            # Create lot for KEPT shares, if any
            qty_kept = total_vest_qty - shares_sold_this_vest_event
            if qty_kept > 0:
                kept_shares_lot = vest_txn.copy()  # Start with original vest info
                kept_shares_lot["quantity"] = qty_kept
                kept_shares_lot["price"] = market_price_for_kept_shares  # Market price from sell, or original if no sell
                kept_shares_lot["transaction_type"] = "BUY"  # Explicitly make it a BUY type if not already
                processed_transactions_for_netting.append(kept_shares_lot)
                logger.info(
                    f"RSU Logic: Recording kept shares for DB: {qty_kept} of {symbol} on {vest_date} @ ${market_price_for_kept_shares}"
                )
            elif total_vest_qty > 0 and shares_sold_this_vest_event >= total_vest_qty:
                logger.info(
                    f"RSU Logic: All {total_vest_qty} vested shares for {symbol} on {vest_date} were sold as part of RSU event. No 'kept' lot to record."
                )

            # Directly calculate and record realized gain for the shares_sold_this_vest_event
            if shares_sold_this_vest_event > 0:
                # Cost basis for these sold shares is original_vest_price (typically $0)
                cost_of_shares_sold_for_rsu_event = shares_sold_this_vest_event * original_vest_price
                rsu_event_realized_gain = total_proceeds_from_rsu_sells - cost_of_shares_sold_for_rsu_event

                # We need to log this into realized_gains table.
                # This is tricky because calculate_realized_gains expects a sell transaction from DB and buy lots.
                # For simplicity here, we'll prepare the data and assume it can be inserted. This part might need a dedicated function or DB call.
                # We use the details from the *first* sell transaction that triggered the market price for consistency if there were multiple.
                # The date of gain is the sell date (vest_date here).
                if sell_transactions_to_skip_db_insert:  # ensure there was at least one sell
                    gain_entry = {
                        "account_id": account_id_for_vest,  # Use account_id from the original vest transaction
                        "symbol": symbol,
                        "quantity_sold": shares_sold_this_vest_event,  # Total qty sold in this event
                        "sell_price_avg": (
                            total_proceeds_from_rsu_sells / shares_sold_this_vest_event if shares_sold_this_vest_event else 0
                        ),
                        "sell_date": vest_date,  # or first_sell_for_gain_calc['trans_time']
                        "cost_basis_avg": original_vest_price,
                        "realized_gain": rsu_event_realized_gain,
                        "related_transaction_type": "RSU_SELL_EVENT",  # Custom marker
                    }
                    # This is where you'd insert gain_entry into realized_gains table.
                    # For now, let's add it to a temporary list on the task object.
                    task.rsu_direct_realized_gains = getattr(task, "rsu_direct_realized_gains", [])
                    task.rsu_direct_realized_gains.append(gain_entry)
                    logger.info(
                        f"RSU Logic: Calculated direct realized gain for {symbol} on {vest_date}: ${rsu_event_realized_gain:.2f} from selling {shares_sold_this_vest_event} shares."
                    )
                else:
                    logger.warning(
                        f"RSU Logic: shares_sold_this_vest_event is {shares_sold_this_vest_event} but no sell_transactions_to_skip_db_insert found. Cannot log gain."
                    )

        # Filter out original RSU vesting transactions as they are now replaced or handled.
        all_transactions_after_rsu_logic = [t for t in processed_transactions_for_netting]
        # Also filter out sell transactions that were part of RSU netting and marked to be skipped for DB insert.
        for txn in other_txns_including_sells:
            if not txn.get("_is_rsu_netted_and_skipped", False):
                all_transactions_after_rsu_logic.append(txn)
            else:
                logger.info(
                    f"RSU Logic: Excluding sell transaction from DB insert: {txn['symbol']} {txn['quantity']} @ {txn['price']} on {txn['trans_time']}"
                )

        all_transactions = all_transactions_after_rsu_logic  # This list goes to validation
        # --- END RSU Netting Pre-processing ---

        # 3. Validate holdings
        task.stage = "validating"
        task.progress = 50

        holdings = {
            row["symbol"]: row["total_quantity"] or 0
            for row in conn.execute(
                "SELECT symbol, SUM(quantity) as total_quantity FROM transactions WHERE account_id = ? GROUP BY symbol",
                (account_id,),
            ).fetchall()
        }

        valid_transactions = []
        total_trans = len(all_transactions)
        all_transactions.sort(key=lambda x: x["trans_time"])

        for i, trans in enumerate(all_transactions):
            symbol = trans["symbol"]
            quantity = trans["quantity"]
            trans_date = trans["trans_time"]
            key = f"{symbol}_{trans_date}"

            # RSU Fix: Check if sell is RSU-related (THIS LOGIC IS NOW MOSTLY OBSOLETE due to pre-processing)
            is_rsu_sell_identified_by_parser = False
            # The flag `_is_rsu_netted_and_skipped` already handles exclusion of these sells from DB.
            # So, the block that used to `continue` based on `_is_rsu_netted_sell` is no longer needed
            # as those transactions are already filtered out from `all_transactions` before this loop.

            if trans.get("is_adjustment", False):
                valid_transactions.append(trans)
                holdings[symbol] = holdings.get(symbol, 0) + quantity
                continue

            holdings[symbol] = holdings.get(symbol, 0)
            valid_transactions.append(trans)
            holdings[symbol] += quantity

            task.progress = 50 + ((i + 1) / total_trans) * 20

        # 4. Import transactions
        task.stage = "importing"
        task.progress = 70

        # Check if initial capital transaction already exists for the earliest date
        # Earliest trans time should be from the potentially filtered all_transactions list
        if all_transactions:  # Ensure list is not empty
            earliest_trans_time = all_transactions[0]["trans_time"]
            initial_cash_exists = conn.execute(
                'SELECT 1 FROM transactions WHERE account_id = ? AND symbol = "CASH" AND date(trans_time) = date(?) LIMIT 1',
                (account_id, earliest_trans_time),
            ).fetchone()

            if not initial_cash_exists:
                # initial_capital should be sum of positive quantity * price from all_transactions going into DB
                current_initial_capital = sum(
                    abs(t["quantity"] * t["price"]) for t in all_transactions if t["quantity"] > 0 and t["symbol"] != "CASH"
                )
                conn.execute(
                    'INSERT OR REPLACE INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (?, "CASH", ?, 1, ?)',
                    (account_id, current_initial_capital, earliest_trans_time),
                )
            else:
                logger.info(f"Initial CASH transaction for {earliest_trans_time} already exists. Skipping.")
        else:  # all_transactions is empty
            logger.info("No transactions remaining after RSU logic to determine earliest_trans_time for initial cash.")
            # Potentially, if task.rsu_direct_realized_gains has items, the initial capital might be 0 or handled differently.

        # Check for duplicates before inserting
        transactions_to_insert = []
        skipped_count = 0
        for t in valid_transactions:
            symbol = symbol_mappings.get(t["symbol"], t["symbol"])
            trans_date = t["trans_time"]  # Already in 'YYYY-MM-DD' format

            # Use ROUND() for price comparison to handle potential floating point inaccuracies
            # Check if a similar transaction exists on the same day
            duplicate_check = conn.execute(
                """
                SELECT 1 FROM transactions
                WHERE account_id = ?
                  AND symbol = ?
                  AND quantity = ?
                  AND ROUND(price, 6) = ROUND(?, 6)
                  AND date(trans_time) = date(?)
                LIMIT 1
            """,
                (t["account_id"], symbol, t["quantity"], t["price"], trans_date),
            ).fetchone()

            if duplicate_check:
                logger.info(
                    f"Skipping duplicate transaction: Acc {t['account_id']}, Sym {symbol}, Qty {t['quantity']}, Price {t['price']}, Date {trans_date}"
                )
                skipped_count += 1
            else:
                transactions_to_insert.append((t["account_id"], symbol, t["quantity"], t["price"], trans_date))

        logger.info(
            f"Identified {len(transactions_to_insert)} new transactions to insert. Skipped {skipped_count} duplicates."
        )

        if transactions_to_insert:
            conn.executemany(
                "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (?, ?, ?, ?, ?)",
                transactions_to_insert,
            )

        task.progress = 80
        conn.commit()
        logger.info("Committed transactions before processing realized gains")

        # Process realized gains only for newly inserted sell transactions
        newly_inserted_sells = [
            t
            for t_data in transactions_to_insert
            for t in valid_transactions
            if t["account_id"] == t_data[0]
            and symbol_mappings.get(t["symbol"], t["symbol"]) == t_data[1]
            and t["quantity"] == t_data[2]
            and round(t["price"], 6) == round(t_data[3], 6)
            and t["trans_time"] == t_data[4]
            and t["quantity"] < 0
        ]

        if newly_inserted_sells:
            logger.info(
                f"Processing realized gains for {len(newly_inserted_sells)} newly inserted sell transactions (non-RSU event sells)."
            )
            sell_batches = split_list(newly_inserted_sells, min(10, len(newly_inserted_sells)))
            threads = [
                threading.Thread(
                    target=process_realized_gains_batch,
                    args=(
                        account_id,
                        batch,
                        i,
                        len(sell_batches),
                        lambda idx, total: setattr(task, "progress", 80 + ((idx + 1) / total) * 15),
                    ),
                )  # Adjusted progress step
                for i, batch in enumerate(sell_batches)
            ]
            for th in threads:
                th.start()
            for th in threads:
                th.join()
        else:
            logger.info("No new non-RSU sell transactions to process for realized gains via standard mechanism.")
            # If no new sells, update progress directly if RSU gains were the only ones
            if hasattr(task, "rsu_direct_realized_gains") and task.rsu_direct_realized_gains:
                task.progress = 95  # Move progress forward as RSU gains were handled
            else:
                task.progress = 80  # No sells, no RSU direct gains, stay at 80 before price cache

        # Insert directly calculated RSU gains
        if hasattr(task, "rsu_direct_realized_gains") and task.rsu_direct_realized_gains:
            logger.info(f"Inserting {len(task.rsu_direct_realized_gains)} direct RSU realized gain entries.")
            # This is a simplified insert. Your realized_gains table structure might require different fields or handling.
            # Example: INSERT INTO realized_gains (account_id, symbol, quantity, sell_price, sell_date, cost_basis, realized_gain, notes)
            # VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            # Ensure your DB schema for realized_gains matches the data in gain_entry dictionary.
            # This is a placeholder for actual DB insertion logic for these direct gains.
            for gain_entry_data in task.rsu_direct_realized_gains:
                try:
                    # Example: You might need to adapt field names and types
                    conn.execute(
                        """
                        INSERT INTO realized_gains (account_id, symbol, quantity_sold, sell_price_avg, sell_date, cost_basis_avg, realized_gain, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                        (
                            gain_entry_data["account_id"],
                            gain_entry_data["symbol"],
                            gain_entry_data["quantity_sold"],
                            gain_entry_data["sell_price_avg"],
                            gain_entry_data["sell_date"],
                            gain_entry_data["cost_basis_avg"],
                            gain_entry_data["realized_gain"],
                            gain_entry_data.get("related_transaction_type", "RSU_EVENT"),
                        ),
                    )
                    logger.info(f"Successfully inserted direct RSU gain for {gain_entry_data['symbol']}")
                except Exception as e_rg_insert:
                    logger.error(
                        f"Failed to insert direct RSU realized gain for {gain_entry_data['symbol']}: {str(e_rg_insert)}",
                        exc_info=True,
                    )
                    task.failed_records.append(
                        {
                            "file": "N/A",
                            "row": "N/A",
                            "error": "Direct RSU Gain Insert Failed",
                            "reason": f"Symbol: {gain_entry_data['symbol']}, Gain: {gain_entry_data['realized_gain']}, Error: {str(e_rg_insert)}",
                        }
                    )
            conn.commit()  # Commit after inserting RSU gains
            task.progress = 95  # Update progress after handling these gains

        task.progress = 95  # Progress after gains calculation (or skipping)

        # 使用调度器中的方法更新价格缓存，以重用逻辑并提高鲁棒性
        logger.info("Updating prices for imported symbols...")
        symbols_to_update = set()
        for trans in valid_transactions:
            if trans["symbol"] != "CASH":
                symbol = symbol_mappings.get(trans["symbol"], trans["symbol"])
                symbols_to_update.add(symbol)

        for symbol in symbols_to_update:
            try:
                logger.info(f"Triggering immediate price update for symbol: {symbol}")
                stock_data_manager.update_stock_data(symbol, conn)
            except Exception as e:
                logger.error(f"Failed to update price for {symbol} during import: {e}", exc_info=True)

        task.success_count = len(transactions_to_insert)  # Update success count based on actual inserts
        account_import_history.setdefault(account_id, []).append(
            {
                "task_id": task_id,
                "start_date": task.start_date,
                "success_count": task.success_count,
                "timestamp": datetime.now(),
            }
        )

        conn.commit()
        task.status = "completed"

    except Exception as e:
        if conn:
            conn.rollback()
        task.error = str(e)
        task.status = "failed"
        logger.error(f"Batch import error: {type(e).__name__}: {str(e)}", exc_info=True)
    finally:
        # Log detailed failed records information
        if hasattr(task, "failed_records") and task.failed_records:
            logger.warning(f"Import task completed with {len(task.failed_records)} failed records")
            for i, record in enumerate(task.failed_records):
                logger.warning(f"Failed record #{i + 1}: {record}")

        if conn:
            conn.close()


def process_rsu_transactions(rsu_components, task):
    """处理RSU相关交易"""
    logger.info(f"Processing {len(rsu_components)} RSU components for task account_id {task.account_id}...")
    rsu_transactions = []

    try:
        # Ensure task.account_id is a valid integer
        task_account_id_int = int(task.account_id)
    except ValueError:
        logger.error(f"Invalid task.account_id format: {task.account_id}. Cannot process RSU transactions for this task.")
        # Add failed records for all RSU components in this task if task_account_id is invalid
        for rsu_comp in rsu_components:
            task.failed_records.append(
                {
                    "file": rsu_comp.get("file_index", "N/A"),
                    "row": rsu_comp.get("row", "N/A"),
                    "error": "任务账户ID无效 (Invalid task account_id for RSU processing)",
                    "reason": f"Task account_id: {task.account_id}",
                }
            )
        return []  # Return empty list as RSU processing cannot proceed

    for rsu in rsu_components:
        current_transaction_account_id = task_account_id_int  # Default to task's account_id

        rsu_specific_account_id_str = rsu.get("account_id")
        if rsu_specific_account_id_str is not None:
            try:
                rsu_specific_account_id_int = int(rsu_specific_account_id_str)
                # If RSU component has a specific account_id, and it's different from the task's,
                # you might decide which one to use. Here, we'll use the RSU's if valid.
                if rsu_specific_account_id_int != task_account_id_int:
                    logger.warning(
                        f"RSU component (row {rsu.get('row', 'N/A')}, file {rsu.get('file_index', 'N/A')}) has account_id {rsu_specific_account_id_int} "
                        f"which differs from task account_id {task_account_id_int}. "
                        f"Using the RSU component's account_id: {rsu_specific_account_id_int} for this transaction."
                    )
                current_transaction_account_id = rsu_specific_account_id_int
            except ValueError:
                logger.warning(
                    f"Invalid account_id format ('{rsu_specific_account_id_str}') in RSU component (row {rsu.get('row', 'N/A')}, file {rsu.get('file_index', 'N/A')}). "
                    f"Defaulting to task account_id {task_account_id_int} for this RSU transaction."
                )
                # current_transaction_account_id remains task_account_id_int (already set as default)

        # Prepare transaction data, using .get() for safety and validating essential fields
        symbol = rsu.get("symbol")
        quantity = rsu.get("quantity")  # Field name in rsu component
        trans_time = rsu.get("trans_time")

        if not all([symbol, quantity is not None, trans_time]):
            error_msg = (
                f"RSU component missing critical data: symbol='{symbol}', quantity='{quantity}', trans_time='{trans_time}'."
            )
            logger.error(f"{error_msg} Original RSU data: {rsu}. Skipping this RSU event.")
            task.failed_records.append(
                {
                    "file": rsu.get("file_index", "N/A"),
                    "row": rsu.get("row", "N/A"),
                    "error": "RSU 事件数据不完整 (RSU event data incomplete)",
                    "reason": error_msg + f" Original RSU data: {rsu}",
                }
            )
            continue  # Skip this RSU component

        transaction = {
            "account_id": current_transaction_account_id,
            "symbol": symbol,
            "quantity": quantity,
            "price": 0,  # RSU vesting usually has no price or a company-defined price
            "trans_time": trans_time,
            "transaction_type": "RSU_VESTING",
            "row": rsu.get("row", 0),  # .get() with default for non-critical fields
            "file_index": rsu.get("file_index", 0),
        }
        rsu_transactions.append(transaction)
        logger.info(
            f"Created RSU vesting transaction for account {current_transaction_account_id}, symbol {transaction['symbol']}: {transaction['quantity']} shares"
        )

    return rsu_transactions


def process_split_transactions_sequential(split_components, account_id, task, conn):
    """处理拆分/反向拆分交易"""
    logger.info(f"Processing {len(split_components)} split components...")

    # 确保 account_id 是整数类型
    try:
        if isinstance(account_id, str):
            account_id = int(account_id)
            logger.debug(f"Converted account_id to integer: {account_id}")
    except ValueError:
        logger.error(f"Invalid account_id format: {account_id}. Must be an integer.")
        return []

    generated_split_transactions = []

    # 按照original_id对拆分组件进行分组
    grouped_splits = defaultdict(lambda: {"FROM": None, "TO": None})
    for comp in split_components:
        # Group primarily by date, account, and ORIGINAL ID
        if comp.get("original_id"):
            if isinstance(comp["account_id"], str):
                comp["account_id"] = int(comp["account_id"])

            key = (comp["trans_time"], comp["account_id"], comp["original_id"])
            if comp["record_type"] == "FROM":
                if grouped_splits[key]["FROM"] is not None:
                    logger.warning(f"Multiple FROM records found for split key {key}. Keeping first.")
                else:
                    grouped_splits[key]["FROM"] = comp
            elif comp["record_type"] == "TO":
                if grouped_splits[key]["TO"] is not None:
                    logger.warning(f"Multiple TO records found for split key {key}. Keeping first.")
                else:
                    grouped_splits[key]["TO"] = comp
            else:
                logger.warning(f"Unknown record_type in split component: {comp}")
        else:
            logger.warning(f"Skipping split component due to missing original_id: {comp}")

    # 日志记录分组结果
    logger.debug(f"Grouped split components (by original_id): {dict(grouped_splits)}")

    # 修复任何不匹配的股票代码
    # 如果它们共享相同的original_id但有不同的tickers，使用FROM的ticker
    for key, components in grouped_splits.items():
        if components.get("FROM") and components.get("TO"):
            from_ticker = components["FROM"].get("ticker")
            to_ticker = components["TO"].get("ticker")
            original_id = components["FROM"].get("original_id")

            # 如果TO组件中的ticker与original_id相同，使用FROM的ticker
            if from_ticker and to_ticker and to_ticker == original_id and from_ticker != to_ticker:
                logger.info(
                    f"Fixing ticker mismatch for split {key}: FROM={from_ticker}, TO={to_ticker}. Setting both to {from_ticker}"
                )
                components["TO"]["ticker"] = from_ticker

    processed_keys = set()
    processed_cusips = set()  # 用于记录处理过的CUSIP，确保价格缓存表清理

    # 处理每一组拆分记录
    if grouped_splits:
        logger.debug(f"Processing {len(grouped_splits)} grouped split pairs.")
        for key, components in grouped_splits.items():
            if key in processed_keys:
                logger.debug(f"Skipping already processed key: {key}")
                continue

            logger.debug(f"Starting processing for split key: {key}")
            from_component = components.get("FROM")
            to_component = components.get("TO")

            if from_component and to_component:
                # 获取TO组件的new_id
                new_id = to_component.get("new_id")
                if not new_id:
                    logger.warning(f"Missing new_id in TO component for key {key}. Skipping pair.")
                    continue

                logger.info(f"Found matching FROM/TO pair for split key {key} -> new_id={new_id}")
                processed_keys.add(key)

                # 处理这一组拆分记录
                try:
                    trans_date, acct_id, original_id = key  # Unpack key

                    symbol_to_query = from_component["ticker"]
                    cusip_to_query = original_id  # CUSIP ID (例如 25460E521)
                    from_quantity = from_component["quantity"]
                    to_quantity = to_component["quantity"]
                    from_quantity_abs = abs(from_quantity)
                    to_quantity_abs = abs(to_quantity)
                    logger.debug(
                        f"Split details - symbol_to_query: {symbol_to_query}, cusip_to_query: {cusip_to_query}, from_qty: {from_quantity}, to_qty: {to_quantity}"
                    )

                    # 记录已处理的CUSIP
                    processed_cusips.add(cusip_to_query)

                    if from_quantity_abs <= 0 or to_quantity_abs <= 0:
                        logger.warning(
                            f"Invalid quantities for split {key}: FROM={from_quantity}, TO={to_quantity}. Skipping."
                        )
                        continue

                    # 重要步骤1: 在进行反向拆分处理前，首先重命名所有旧CUSIP为新符号
                    logger.info(
                        f"Step 1: Handling symbol renaming and cache for {cusip_to_query} -> {symbol_to_query} for account {acct_id}"
                    )
                    try:
                        # 记录当前状态
                        logger.debug(f"PRE-QUERY CHECK: account_id = {acct_id} (type: {type(acct_id)})")
                        logger.debug(f"PRE-QUERY CHECK: cusip_to_query = '{cusip_to_query}' (type: {type(cusip_to_query)})")

                        # 查询使用旧CUSIP的交易
                        cusip_transactions = conn.execute(
                            """
                            SELECT transaction_id, quantity, price, trans_time
                            FROM transactions
                            WHERE account_id = ? AND symbol = ?
                        """,
                            (acct_id, cusip_to_query),
                        ).fetchall()  # 精确匹配
                        logger.debug(f"Found {len(cusip_transactions)} exact matching CUSIP transactions.")

                        # 如果没有找到，尝试使用LIKE查询
                        if not cusip_transactions:
                            logger.debug(f"Trying with LIKE for: symbol LIKE '{cusip_to_query}%'")
                            cusip_transactions = conn.execute(
                                """
                                SELECT transaction_id, quantity, price, trans_time
                                FROM transactions
                                WHERE account_id = ? AND symbol LIKE ?
                            """,
                                (acct_id, cusip_to_query + "%"),
                            ).fetchall()
                            logger.debug(f"Found {len(cusip_transactions)} matching CUSIP transactions with LIKE.")

                        # 如果找到了使用旧CUSIP的交易，将它们重命名为新符号
                        if cusip_transactions:
                            logger.info(
                                f"Found {len(cusip_transactions)} transactions using CUSIP {cusip_to_query}. Renaming to {symbol_to_query}"
                            )

                            # 更新所有使用旧CUSIP的交易
                            cursor = conn.execute(
                                """
                                UPDATE transactions
                                SET symbol = ?
                                WHERE account_id = ? AND symbol = ?
                            """,
                                (symbol_to_query, acct_id, cusip_to_query),
                            )

                            # 如果没有影响行，尝试使用LIKE
                            if cursor.rowcount == 0:
                                cursor = conn.execute(
                                    """
                                    UPDATE transactions
                                    SET symbol = ?
                                    WHERE account_id = ? AND symbol LIKE ?
                                """,
                                    (symbol_to_query, acct_id, cusip_to_query + "%"),
                                )

                            logger.info(f"Executed UPDATE on transactions table. Rows affected: {cursor.rowcount}")

                            # 价格数据由Redis缓存管理
                            logger.info(f"Symbol mapping updated from {cusip_to_query} to {symbol_to_query}")
                            logger.info(f"Removed {cursor.rowcount} price records for {cusip_to_query}.")
                        else:
                            logger.debug(f"Holdings check found no holdings for {cusip_to_query} for account {acct_id}.")

                        # 更新全局映射字典
                        if cusip_to_query not in symbol_mappings:
                            symbol_mappings[cusip_to_query] = symbol_to_query
                            logger.info(f"Added global symbol mapping: {cusip_to_query} -> {symbol_to_query}")
                        else:
                            logger.debug(
                                f"Global symbol mapping {cusip_to_query} -> {symbol_mappings[cusip_to_query]} already exists."
                            )

                    except Exception as e_rename:
                        logger.error(
                            f"Error during symbol renaming/cache update for {cusip_to_query} -> {symbol_to_query}: {str(e_rename)}",
                            exc_info=True,
                        )
                        raise  # 重新抛出异常

                    # 步骤2: 使用新符号处理拆分的成本调整
                    logger.info(f"Step 2: Processing cost basis adjustment for {symbol_to_query} on {trans_date}")
                    try:
                        # 查询使用新符号的买入记录
                        buy_lots = []
                        buy_lots = conn.execute(
                            """
                            SELECT transaction_id, quantity, price, trans_time
                            FROM transactions
                            WHERE account_id = ? AND symbol = ? AND quantity > 0 AND trans_time < ?
                            ORDER BY trans_time ASC, transaction_id ASC
                        """,
                            (acct_id, symbol_to_query, trans_date),
                        ).fetchall()
                        logger.debug(f"Found {len(buy_lots)} buy lots for {symbol_to_query}.")

                        # FIFO逻辑
                        cost_basis_removed = 0.0
                        quantity_to_remove = abs(to_component["quantity"])
                        original_shares_to_remove = quantity_to_remove
                        lots_consumed = []

                        for lot in buy_lots:
                            lot_qty = lot["quantity"]
                            if quantity_to_remove <= 0:
                                break  # 所有份额已处理

                            if lot_qty <= quantity_to_remove:
                                # 使用整个批次
                                qty_from_lot = lot_qty
                                quantity_to_remove -= qty_from_lot
                                cost_basis_removed += qty_from_lot * lot["price"]
                                lots_consumed.append(lot["transaction_id"])
                                logger.debug(
                                    f"FIFO: Used entire lot {lot['transaction_id']} with {qty_from_lot} shares at ${lot['price']}"
                                )
                            else:
                                # 使用部分批次
                                qty_from_lot = quantity_to_remove
                                quantity_to_remove = 0
                                cost_basis_removed += qty_from_lot * lot["price"]
                                lots_consumed.append(lot["transaction_id"])
                                logger.debug(
                                    f"FIFO: Used {qty_from_lot} shares from lot {lot['transaction_id']} at ${lot['price']}"
                                )

                        # 计算实际移除的份额（如果找不到足够的买入记录，也要确保移除原始份额）
                        actual_shares_removed = original_shares_to_remove - quantity_to_remove

                        # 如果没有找到任何买入记录或找到的不足，仍然使用原始移除数量
                        # 这是修复25460E521 -> YANG反向拆分的关键
                        if actual_shares_removed == 0:
                            logger.warning(
                                f"No buy lots found for {symbol_to_query} but proceeding with removal of {original_shares_to_remove} shares as part of reverse split"
                            )
                            actual_shares_removed = original_shares_to_remove
                            # 使用当前原始CUSIP交易的价格作为成本基础
                            # 检查数据库中是否有这个符号的交易记录
                            existing_tx = conn.execute(
                                """
                                SELECT price FROM transactions
                                WHERE symbol = ? AND quantity > 0
                                ORDER BY trans_time DESC LIMIT 1
                            """,
                                (symbol_to_query,),
                            ).fetchone()

                            if existing_tx:
                                cost_basis_per_share = existing_tx["price"]
                                cost_basis_removed = cost_basis_per_share * actual_shares_removed
                                logger.info(f"Using existing price ${cost_basis_per_share} for cost basis calculation")
                            else:
                                # 如果没有，使用to_component中的价格估计
                                avg_price = abs(to_component.get("price", 0)) or 2.59  # 如果价格为0则使用默认值
                                cost_basis_removed = avg_price * actual_shares_removed
                                logger.info(
                                    f"No existing transactions found, using estimated price ${avg_price} for cost basis"
                                )

                        # 计算新股价
                        new_shares_to_add = abs(from_component["quantity"])
                        cost_basis_per_original_share = (
                            (cost_basis_removed / actual_shares_removed) if actual_shares_removed > 0 else 0
                        )
                        new_price = (cost_basis_removed / new_shares_to_add) if new_shares_to_add > 0 else 0
                        logger.info(
                            f"Reverse split {symbol_to_query}: Cost basis ${cost_basis_removed:.2f} for {actual_shares_removed} original shares. New price for {new_shares_to_add} shares: ${new_price:.6f}"
                        )

                        # 生成调整交易
                        removal_transaction = {
                            "account_id": account_id,
                            "symbol": symbol_to_query,
                            "quantity": -original_shares_to_remove,  # 负数表示移除完整的原始份额
                            "price": cost_basis_per_original_share,
                            "trans_time": trans_date,
                            "is_adjustment": True,
                            "row": from_component.get("row", 0),
                            "file_index": from_component.get("file_index", 0),
                        }
                        addition_transaction = {
                            "account_id": account_id,
                            "symbol": symbol_to_query,
                            "quantity": new_shares_to_add,  # 正数表示添加
                            "price": new_price,
                            "trans_time": trans_date,
                            "is_adjustment": True,
                            "row": to_component.get("row", 0),
                            "file_index": to_component.get("file_index", 0),
                        }

                        generated_split_transactions.append(removal_transaction)
                        generated_split_transactions.append(addition_transaction)
                        logger.info(f"Added adjustment transactions for {symbol_to_query} to the list.")
                        logger.info(
                            f"SPLIT DEBUG: Created removal transaction: {symbol_to_query}, {-original_shares_to_remove} shares at ${cost_basis_per_original_share:.6f}"
                        )
                        logger.info(
                            f"SPLIT DEBUG: Created addition transaction: {symbol_to_query}, {new_shares_to_add} shares at ${new_price:.6f}"
                        )
                    except Exception as e_split_fifo:
                        logger.error(
                            f"Error during split FIFO calculation for {symbol_to_query}: {str(e_split_fifo)}",
                            exc_info=True,
                        )
                        raise  # 重新抛出异常

                except Exception as e_split_main:
                    logger.error(f"General error processing split group ({key}): {str(e_split_main)}", exc_info=True)
            elif from_component and not to_component:
                logger.warning(f"Found FROM component but no matching TO for split key {key}. Skipping.")
            elif not from_component and to_component:
                logger.warning(f"Found TO component but no matching FROM for split key {key}. Skipping.")

    # 最终清理
    logger.info(f"Step 3: Final price cache and transaction cleanup for {len(processed_cusips)} CUSIPs.")
    try:
        cleaned_count = 0
        trans_cleaned_count = 0

        # 清理所有可能仍在使用CUSIP的交易和价格记录
        for cusip in processed_cusips:
            if cusip in symbol_mappings:
                # 更新所有使用该CUSIP的交易
                cursor = conn.execute(
                    """
                    UPDATE transactions
                    SET symbol = ?
                    WHERE symbol = ?
                """,
                    (symbol_mappings[cusip], cusip),
                )

                if cursor.rowcount > 0:
                    trans_cleaned_count += cursor.rowcount
                    logger.info(
                        f"Final cleanup updated {cursor.rowcount} transactions from {cusip} to {symbol_mappings[cusip]}"
                    )

            # 价格缓存由Redis管理
            logger.debug(f"CUSIP {cusip} cleanup completed")

        logger.info(
            f"Final cleanup summary: Updated {trans_cleaned_count} transactions, removed {cleaned_count} price records."
        )

    except Exception as e_cleanup:
        logger.error(f"Error during final cleanup: {str(e_cleanup)}", exc_info=True)
        # 继续处理，不要因为清理失败而中断整个过程

    logger.info(f"Split processing completed. Generated {len(generated_split_transactions)} transactions.")
    return generated_split_transactions


@transactions_bp.route("/import-status/<job_id>", methods=["GET"], strict_slashes=False)
def get_import_status(job_id):
    task = import_tasks.get(job_id)
    if not task:
        return jsonify({"error": "Import job not found"}), 404

    response = {
        "status": task.status,
        "stage": task.stage,
        "progress": task.progress,
        "success_count": task.success_count,
        "failed_records": task.failed_records,
    }

    if task.error:
        response["error"] = task.error

    if task.status in ["completed", "failed"]:
        import_tasks.pop(job_id, None)

    return jsonify(response)


@transactions_bp.route("/upload", methods=["POST"], strict_slashes=False)
def upload_transactions():
    if "files[]" not in request.files:
        return jsonify({"error": "No files provided"}), 400

    files = request.files.getlist("files[]")
    if not all(f.filename and f.filename.endswith(".csv") for f in files):
        return jsonify({"error": "Invalid file type, all files must be CSV"}), 400

    account_id = request.form.get("account_id")
    if not account_id:
        return jsonify({"error": "No account_id provided"}), 400

    task = ImportTask(account_id, True)
    import_tasks[task.id] = task

    files_content = []
    for file in files:
        try:
            content = file.read().decode("utf-8")
            files_content.append(content)
        except UnicodeDecodeError:
            return jsonify({"error": f"File {file.filename} is not a valid UTF-8 encoded file"}), 400

    # 启动一个后台线程处理导入任务
    thread = threading.Thread(target=process_batch_import_task, args=(task.id, files_content, account_id))
    thread.daemon = True  # 将线程设为守护线程，确保主程序退出时线程也会结束
    thread.start()

    return jsonify({"message": "Batch import started", "job_id": task.id, "total_files": len(files)}), 202


# 并行处理交易的主函数
def parallel_process_transactions(task_id, components_dict, account_id, conn=None):
    """
    处理不同类型的交易组件，RSU使用并行处理，但拆分交易使用顺序处理

    Args:
        task_id: 任务ID
        components_dict: 包含不同类型交易组件的字典
        account_id: 账户ID
        conn: 数据库连接对象

    Returns:
        所有处理完成的交易列表
    """
    task = import_tasks[task_id]
    results = {}
    threads = []

    # 定义线程安全的结果存储
    results_lock = threading.Lock()

    # 处理RSU交易的线程函数
    def rsu_thread_func():
        # Create thread-local connection for RSU processing
        thread_conn = None
        try:
            thread_conn = get_db_connection()
            if "rsu" in components_dict and components_dict["rsu"]:
                logger.info(f"Starting RSU processing thread with {len(components_dict['rsu'])} components")
                rsu_results = process_rsu_transactions(components_dict["rsu"], task)

                with results_lock:
                    results["rsu"] = rsu_results

                logger.info(f"RSU processing thread completed with {len(rsu_results)} transactions")
            else:
                with results_lock:
                    results["rsu"] = []
        except Exception as e:
            logger.error(f"Error in RSU processing thread: {str(e)}", exc_info=True)
            with results_lock:
                results["rsu"] = []
        finally:
            if thread_conn:
                thread_conn.close()

    # 启动RSU处理线程
    rsu_thread = threading.Thread(target=rsu_thread_func)
    threads.append(rsu_thread)
    rsu_thread.start()

    # 等待RSU线程完成
    for thread in threads:
        thread.join()

    # 顺序处理拆分交易，使用提供的数据库连接
    if "split" in components_dict and components_dict["split"]:
        logger.info(f"Starting sequential split processing with {len(components_dict['split'])} components")
        try:
            split_results = process_split_transactions_sequential(components_dict["split"], account_id, task, conn)
            results["split"] = split_results
            logger.info(f"Split processing completed with {len(split_results)} transactions")
        except Exception as e:
            logger.error(f"Error in split processing: {str(e)}", exc_info=True)
            results["split"] = []
    else:
        results["split"] = []

    # 汇总所有处理结果
    all_transactions = []

    # 按特定顺序添加交易：先RSU，然后拆分/反向拆分，最后是常规交易
    if "rsu" in results and results["rsu"]:
        all_transactions.extend(results["rsu"])
        logger.info(f"Added {len(results['rsu'])} RSU transactions to final results")

    if "split" in results and results["split"]:
        all_transactions.extend(results["split"])
        logger.info(f"Added {len(results['split'])} split transactions to final results")
        # Add detailed logging for split transactions
        for idx, trans in enumerate(results["split"]):
            logger.info(
                f"SPLIT DEBUG: Split transaction {idx + 1}/{len(results['split'])}: {trans['symbol']}, {trans['quantity']} shares, is_adjustment={trans.get('is_adjustment', False)}"
            )

    if "regular" in components_dict and components_dict["regular"]:
        all_transactions.extend(components_dict["regular"])
        logger.info(f"Added {len(components_dict['regular'])} regular transactions to final results")

    # 按日期排序
    if all_transactions:
        all_transactions.sort(key=lambda x: x["trans_time"])
        logger.info(f"Sorted {len(all_transactions)} total transactions by date")

    return all_transactions


def split_list(items, num_chunks):
    """Split a list into approximately equal chunks"""
    chunk_size = max(1, len(items) // num_chunks)
    return [items[i : i + chunk_size] for i in range(0, len(items), chunk_size)]


def process_realized_gains_batch(account_id, batch, batch_index, total_batches, progress_callback):
    """Process a batch of sell transactions to calculate realized gains"""
    # Create a new connection for this thread instead of using the one from parent thread
    thread_conn = None
    try:
        thread_conn = get_db_connection()

        # Ensure we're only processing sell transactions (negative quantity)
        sell_transactions = [t for t in batch if t["quantity"] < 0]
        if not sell_transactions:
            logger.warning(f"Batch {batch_index} contains no sell transactions to process")
            return

        logger.info(f"Processing {len(sell_transactions)} sell transactions in batch {batch_index}")

        for i, trans in enumerate(sell_transactions):
            try:
                logger.info(
                    f"Processing realized gains for {trans['symbol']} on {trans['trans_time']}, quantity: {trans['quantity']}"
                )

                # First check if this transaction's realized gains have already been calculated
                existing = thread_conn.execute(
                    """
                    SELECT COUNT(*) FROM realized_gains
                    WHERE account_id = ? AND symbol = ? AND sell_date = ?
                    AND sell_price = ?
                """,
                    [account_id, trans["symbol"], trans["trans_time"], trans["price"]],
                ).fetchone()[0]

                if existing > 0:
                    logger.info(f"Skipping already processed transaction for {trans['symbol']} on {trans['trans_time']}")
                    continue

                # Calculate and insert realized gains
                realized_gain = calculate_realized_gains(
                    thread_conn,
                    account_id,
                    {
                        "symbol": trans["symbol"],
                        "quantity": trans["quantity"],
                        "price": trans["price"],
                        "trans_time": trans["trans_time"],
                    },
                )

                logger.debug(f"Calculated realized gain: ${realized_gain:.2f} for {trans['symbol']}")

                # Verify if the insertion was successful
                count = thread_conn.execute(
                    "SELECT COUNT(*) FROM realized_gains WHERE account_id = ? AND symbol = ? AND sell_date = ?",
                    [account_id, trans["symbol"], trans["trans_time"]],
                ).fetchone()[0]
                logger.debug(f"Inserted {count} realized_gains records for {trans['symbol']} on {trans['trans_time']}")

                # Update progress
                overall_index = batch_index * len(sell_transactions) + i
                progress_callback(overall_index, len(sell_transactions) * total_batches)

            except Exception as e:
                logger.exception(f"Error processing realized gains for {trans['symbol']}: {str(e)}")
                # Continue processing other transactions in the batch

        # Commit after all transactions in this batch are processed
        logger.info(f"Committing batch {batch_index}")
        thread_conn.commit()

    except Exception as e:
        if thread_conn:
            thread_conn.rollback()
        logger.error(f"Error processing batch {batch_index}: {str(e)}", exc_info=True)
    finally:
        if thread_conn:
            thread_conn.close()
