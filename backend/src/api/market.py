import json

import requests
from flask import jsonify, request

from ..utils.cache import get_stock_cache
from ..utils.calculations import calculate_current_market_value
from ..utils.logger import get_logger
from . import get_db_connection, market_bp

logger = get_logger()


@market_bp.route("/hotspots", strict_slashes=False)
def get_market_hotspots():
    conn = None
    try:
        conn = get_db_connection()
        # 获取当前持仓的股票代码（数量大于0的）
        symbols = [
            row["symbol"]
            for row in conn.execute(
                """
            SELECT DISTINCT t.symbol
            FROM transactions t
            GROUP BY t.symbol
            HAVING SUM(t.quantity) > 0 AND t.symbol != 'CASH'
        """
            ).fetchall()
        ]

        # 使用统一缓存批量获取价格数据
        stock_cache = get_stock_cache()
        current_prices = stock_cache.get_portfolio_prices(symbols)

        # 获取股票信息
        stocks_data = []
        for symbol in symbols:
            try:
                # 使用缓存获取当前价格
                current_price = current_prices.get(symbol)
                if current_price is None:
                    logger.warning(f"No current price found for {symbol}")
                    continue

                # 使用通用函数计算市值和收益率
                current_value, total_cost, _ = calculate_current_market_value(conn, symbol)

                # 计算平均成本作为基准价格
                holdings = conn.execute(
                    """
                    SELECT SUM(quantity) as total_quantity
                    FROM transactions
                    WHERE symbol = ?
                    GROUP BY symbol
                    HAVING SUM(quantity) > 0
                """,
                    (symbol,),
                ).fetchone()

                if holdings and holdings["total_quantity"] > 0:
                    avg_cost = total_cost / holdings["total_quantity"]
                    change_percent = ((current_price - avg_cost) / avg_cost) * 100 if avg_cost > 0 else 0

                    stocks_data.append(
                        {
                            "symbol": symbol,
                            "name": symbol,  # 使用symbol作为名称，因为这只是显示用途
                            "price": current_price,
                            "change_percent": change_percent,
                            "quantity": holdings["total_quantity"],
                            "avg_cost": avg_cost,
                            "total_value": current_value,
                        }
                    )
            except Exception as e:
                print(f"Error processing data for {symbol}: {str(e)}")
                continue

        # 按涨跌幅排序
        stocks_data.sort(key=lambda x: x["change_percent"])

        # 获取前5个跌幅和后5个涨幅
        losers = stocks_data[:5]
        gainers = stocks_data[-5:][::-1]  # 反转顺序使最大涨幅在前

        return jsonify({"gainers": gainers, "losers": losers})

    except Exception as e:
        print(f"Error in get_market_hotspots: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        if "conn" in locals() and conn:
            conn.close()


@market_bp.route("/fear-greed-index", methods=["GET"], strict_slashes=False)
def get_fear_greed_index():
    cnn_url = "https://production.dataviz.cnn.io/index/fearandgreed/graphdata"

    # Headers mimicking the Puppeteer script
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
    }

    response = None
    try:
        response = requests.get(cnn_url, headers=headers, timeout=30)  # Add headers and timeout
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)

        # Try to parse the JSON data
        data = response.json()

        # Extract the specific data needed by the frontend
        fng_data = data.get("fear_and_greed", {})

        # Round the score if it exists and is numeric
        score_value = fng_data.get("score")
        rounded_score = None
        if isinstance(score_value, (int, float)):
            # Round to nearest integer
            rounded_score = round(score_value)

        extracted_data = {
            "value": rounded_score,  # Use the rounded score
            "description": fng_data.get("rating"),
            "timestamp": fng_data.get("timestamp"),
        }

        return jsonify(extracted_data)  # Return only the extracted data

    except requests.exceptions.RequestException as e:
        # Log connection errors, timeouts, or HTTP errors (like 418)
        logger.exception(f"Error fetching Fear & Greed Index data from {cnn_url}: {e}")
        # Return a more specific error if possible, otherwise generic
        error_msg = (
            f"Failed to fetch data from CNN: {e}"
            if e.response is None
            else f"Failed to fetch data from CNN (Status: {e.response.status_code})"
        )
        status_code = e.response.status_code if e.response is not None else 500
        return jsonify({"error": error_msg}), status_code if status_code >= 400 else 500  # Ensure valid HTTP status

    except json.JSONDecodeError as e:
        # Log error if the response was successful (2xx) but not valid JSON
        response_text = response.text[:200] if response else "No response available"
        logger.error(f"Error decoding JSON from CNN: {e}. Response text: {response_text}...")  # Log beginning of text
        return jsonify({"error": "Invalid data format received from CNN"}), 500

    except Exception as e:  # Catch any other unexpected Python errors
        logger.exception(f"Unexpected error in get_fear_greed_index: {e}")
        return jsonify({"error": "An unexpected error occurred"}), 500
