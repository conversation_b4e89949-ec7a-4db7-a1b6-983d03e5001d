"""
Unified high-performance stock cache system
Integrates Redis + batch processing + multi-level cache
"""

import asyncio
import atexit
import json
import os
import pickle
import signal
import threading
import time
from collections import OrderedDict
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Union

import pandas as pd
import yfinance as yf
from yahooquery import Ticker

from .logger import get_logger

logger = get_logger()


def get_vix_ticker():
    """
    Get VIX ticker instance for market volatility data.

    Returns:
        yfinance.Ticker: VIX ticker object for fetching volatility data
    """
    return yf.Ticker("^VIX")


class DataType(Enum):
    """数据类型枚举"""

    PRICE = "price"
    HISTORICAL = "historical"


@dataclass
class CacheConfig:
    """缓存配置"""

    # Redis配置
    redis_url: str = field(default_factory=lambda: os.getenv("REDIS_URL", "redis://localhost:6379/1"))
    redis_max_connections: int = field(default_factory=lambda: int(os.getenv("REDIS_MAX_CONNECTIONS", "50")))
    redis_connection_timeout: int = field(default_factory=lambda: int(os.getenv("REDIS_TIMEOUT", "5")))
    redis_socket_timeout: int = field(default_factory=lambda: int(os.getenv("REDIS_SOCKET_TIMEOUT", "5")))

    # TTL配置 (秒)
    price_ttl: int = 300  # 价格缓存5分钟
    historical_ttl: int = 3600  # 历史数据缓存1小时

    # 批量处理配置
    batch_size: int = 50
    max_workers: int = 20

    # L1内存缓存配置
    l1_max_size: int = 1000
    l1_ttl: int = 60

    # 性能优化配置
    compression_enabled: bool = True
    circuit_breaker_threshold: int = 5  # 断路器阈值


@dataclass
class CacheEntry:
    """缓存条目"""

    data: Any
    timestamp: float
    ttl: int
    access_count: int = 0
    last_access: float = field(default_factory=time.time)

    def is_expired(self) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > self.ttl

    def touch(self):
        """更新访问时间"""
        self.last_access = time.time()
        self.access_count += 1


class L1MemoryCache:
    """L1内存缓存"""

    def __init__(self, max_size: int = 1000, default_ttl: int = 60):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        with self._lock:
            if key in self.cache:
                entry = self.cache[key]
                if not entry.is_expired():
                    entry.touch()
                    # 移到末尾（LRU）
                    self.cache.move_to_end(key)
                    return entry.data
                else:
                    # 删除过期条目
                    del self.cache[key]
            return None

    def set(self, key: str, data: Any, ttl: int = None) -> None:
        """设置缓存数据"""
        with self._lock:
            ttl = ttl or self.default_ttl
            entry = CacheEntry(data=data, timestamp=time.time(), ttl=ttl)

            # 如果key已存在，更新它
            if key in self.cache:
                del self.cache[key]

            # 添加新条目
            self.cache[key] = entry

            # 检查大小限制
            while len(self.cache) > self.max_size:
                # 删除最旧的条目
                self.cache.popitem(last=False)

    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self.cache.clear()

    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)


class StockCache:
    """统一的股票缓存系统"""

    _instance = None
    _initialized = False

    def __new__(cls, cache_dir=None, cache_expiry=3600, config: CacheConfig = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, cache_dir=None, cache_expiry=3600, config: CacheConfig = None):
        """初始化统一的股票缓存系统"""
        # 防止重复初始化
        if self._initialized:
            return

        # 向后兼容的参数
        self._cache_dir = cache_dir
        self._cache_expiry = cache_expiry
        self.logger = get_logger()

        # 初始化缓存配置
        self.config = config or CacheConfig()
        self.redis_client = None
        self.redis_pool = None
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)

        # L1内存缓存
        self.l1_cache = L1MemoryCache(max_size=self.config.l1_max_size, default_ttl=self.config.l1_ttl)

        # 初始化Redis
        self._initialize_redis()

        # 缓存统计
        self.cache_stats = {
            "l1_hits": 0,
            "l1_misses": 0,
            "l2_hits": 0,
            "l2_misses": 0,
            "batch_requests": 0,
            "prefetch_requests": 0,
            "total_requests": 0,
            "avg_response_time": 0.0,
            "redis_errors": 0,
            "api_errors": 0,
            "circuit_breaker_trips": 0,
        }

        # Redis健康状态
        self.redis_healthy = True
        self.redis_error_count = 0
        self.circuit_breaker_last_trip = 0

        # 注册清理函数
        atexit.register(self.shutdown)

        # 标记为已初始化
        self._initialized = True

    def _initialize_redis(self):
        """初始化Redis连接 - 单例模式避免重复初始化"""
        # 如果已经初始化过，直接返回
        if hasattr(self, "redis_client") and self.redis_client is not None:
            return

        try:
            import redis

            # 解析Redis URL
            if self.config.redis_url.startswith("redis://"):
                self.redis_client = redis.from_url(
                    self.config.redis_url,
                    max_connections=self.config.redis_max_connections,
                    socket_timeout=self.config.redis_socket_timeout,
                    socket_connect_timeout=self.config.redis_connection_timeout,
                    decode_responses=False,  # 保持二进制模式以支持pickle
                )
            else:
                # 本地Redis配置
                self.redis_client = redis.Redis(
                    host="localhost",
                    port=6379,
                    db=1,
                    max_connections=self.config.redis_max_connections,
                    socket_timeout=self.config.redis_socket_timeout,
                    socket_connect_timeout=self.config.redis_connection_timeout,
                    decode_responses=False,
                )

            # 测试连接
            self.redis_client.ping()
            self.redis_healthy = True
            logger.info("Redis cache initialized successfully")

        except Exception as e:
            logger.warning(f"Redis initialization failed: {e}. Using L1 cache only.")
            self.redis_client = None
            self.redis_healthy = False

    def _handle_redis_error(self):
        """处理Redis错误"""
        self.redis_error_count += 1
        self.cache_stats["redis_errors"] += 1

        if self.redis_error_count >= self.config.circuit_breaker_threshold:
            self.redis_healthy = False
            self.circuit_breaker_last_trip = time.time()
            self.cache_stats["circuit_breaker_trips"] += 1
            logger.warning("Redis marked as unhealthy due to repeated errors")

    def _check_redis_health(self):
        """检查Redis健康状态"""
        try:
            if self.redis_client is None:
                # Redis客户端不可用
                self._handle_redis_error()
                return False

            self.redis_client.ping()
            # 重置错误计数器如果健康检查成功
            self.redis_error_count = 0
            self.redis_healthy = True
            return True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            self._handle_redis_error()
        return False

    def _get_cache_key(self, data_type: DataType, symbol: str, extra: str = None) -> str:
        """生成缓存键"""
        key = f"stock_cache:{data_type.value}:{symbol}"
        if extra:
            key += f":{extra}"
        return key

    def _get_ttl_for_type(self, data_type: DataType) -> int:
        """根据数据类型获取TTL"""
        ttl_mapping = {
            DataType.PRICE: self.config.price_ttl,
            DataType.HISTORICAL: self.config.historical_ttl,
        }
        return ttl_mapping.get(data_type, 3600)  # 默认1小时

    def get_multi_level(self, key: str, data_type: DataType) -> Optional[Any]:
        """多级缓存获取"""
        self.cache_stats["total_requests"] += 1

        # L1缓存检查
        data = self.l1_cache.get(key)
        if data is not None:
            self.cache_stats["l1_hits"] += 1
            return data

        self.cache_stats["l1_misses"] += 1

        # L2 Redis缓存检查
        if self.redis_healthy and self.redis_client:
            try:
                redis_data = self.redis_client.get(key)
                if redis_data:
                    if self.config.compression_enabled:
                        data = pickle.loads(redis_data)
                    else:
                        data = json.loads(redis_data.decode("utf-8"))

                    # 回填L1缓存
                    self.l1_cache.set(key, data, min(self._get_ttl_for_type(data_type), self.config.l1_ttl))
                    self.cache_stats["l2_hits"] += 1
                    return data
            except Exception as e:
                logger.error(f"Redis get error: {e}")
                self._handle_redis_error()

        self.cache_stats["l2_misses"] += 1
        return None

    def set_multi_level(self, key: str, data: Any, data_type: DataType) -> None:
        """多级缓存设置"""
        ttl = self._get_ttl_for_type(data_type)

        # 设置L1缓存
        self.l1_cache.set(key, data, min(ttl, self.config.l1_ttl))

        # 设置L2 Redis缓存
        if self.redis_healthy and self.redis_client:
            try:
                if self.config.compression_enabled:
                    serialized_data = pickle.dumps(data)
                else:
                    serialized_data = json.dumps(data, default=str).encode("utf-8")

                self.redis_client.setex(key, ttl, serialized_data)
            except Exception as e:
                logger.error(f"Redis set error: {e}")
                self._handle_redis_error()

    def delete_multi_level(self, key: str) -> None:
        """多级缓存删除"""
        # 删除L1缓存
        self.l1_cache.delete(key)

        # 删除L2 Redis缓存
        if self.redis_healthy and self.redis_client:
            try:
                self.redis_client.delete(key)
            except Exception as e:
                logger.error(f"Redis delete error: {e}")
                self._handle_redis_error()

    def get_batch_data_optimized(
        self, symbols: List[str], include_historical: bool = True, include_prices: bool = True
    ) -> Dict[str, Any]:
        """优化的批量数据获取 - 统一接口"""
        result = {}

        if include_prices and symbols:
            # 使用优化的批量价格获取
            prices = self._get_current_prices_sync(symbols)
            result["prices"] = prices

        if include_historical and symbols:
            # 使用优化的批量历史数据获取
            historical_data = self.get_historical_data_batch(symbols)
            result["historical"] = historical_data

        return result

    def warm_cache_for_symbols(self, symbols: List[str]) -> Dict[str, bool]:
        """为指定符号预热缓存"""
        results = {}

        if not symbols:
            return results

        try:
            # 批量获取价格数据并缓存
            prices = self._get_current_prices_sync(symbols)

            # 批量获取历史数据并缓存
            historical_data = self.get_historical_data_batch(symbols)

            for symbol in symbols:
                has_price = symbol in prices
                has_historical = symbol in historical_data
                results[symbol] = has_price or has_historical

        except Exception as e:
            logger.error(f"Error warming cache for symbols {symbols}: {e}")
            for symbol in symbols:
                results[symbol] = False

        return results

    def get_cache_health(self) -> Dict[str, Any]:
        """获取缓存系统健康状态"""
        health_info = {
            "redis_healthy": self.redis_healthy,
            "l1_cache_size": len(self.l1_cache.cache) if hasattr(self.l1_cache, "cache") else 0,
            "timestamp": time.time(),
        }

        # Redis连接统计
        if self.redis_healthy and self.redis_client:
            try:
                # 简化的Redis健康检查
                self.redis_client.ping()
                health_info["redis_info"] = {"status": "connected", "ping_successful": True}
            except Exception as e:
                health_info["redis_error"] = str(e)

        # L1缓存统计
        health_info["stats"] = {
            "l1_cache_enabled": True,
            "redis_enabled": self.redis_healthy,
            "compression_enabled": self.config.compression_enabled,
        }

        return health_info

    def sync_with_file_cache(self, cache_dir: str, symbols: List[str] = None) -> Dict[str, int]:
        """与文件缓存同步 - 统一缓存系统的第二步"""
        from pathlib import Path

        import pandas as pd

        results = {"synced": 0, "errors": 0, "skipped": 0}
        cache_path = Path(cache_dir)

        if not cache_path.exists():
            logger.warning(f"Cache directory {cache_dir} does not exist")
            return results

        # 如果没有指定符号，扫描所有parquet文件
        if symbols is None:
            parquet_files = list(cache_path.glob("*_data.parquet"))
            symbols = [f.stem.replace("_data", "") for f in parquet_files]

        for symbol in symbols:
            try:
                cache_file = cache_path / f"{symbol}_data.parquet"
                if cache_file.exists():
                    # 读取parquet文件
                    df = pd.read_parquet(cache_file)
                    if not df.empty:
                        # 将历史数据同步到Redis缓存
                        cache_key = self._get_cache_key(DataType.HISTORICAL, symbol)
                        ttl = self._get_ttl_for_type(DataType.HISTORICAL)

                        # 存储到L1和L2缓存
                        self.l1_cache.set(cache_key, df, min(ttl, self.config.l1_ttl))

                        if self.redis_healthy and self.redis_client:
                            try:
                                if self.config.compression_enabled:
                                    serialized_data = pickle.dumps(df)
                                else:
                                    serialized_data = df.to_json().encode("utf-8")
                                self.redis_client.setex(cache_key, ttl, serialized_data)
                            except Exception as e:
                                logger.error(f"Error syncing {symbol} to Redis: {e}")

                        results["synced"] += 1
                    else:
                        results["skipped"] += 1
                else:
                    results["skipped"] += 1

            except Exception as e:
                logger.error(f"Error syncing {symbol} from file cache: {e}")
                results["errors"] += 1

        logger.info(f"File cache sync completed: {results}")
        return results

    def unified_cache_sync(self, cache_dir: str = None, symbols: List[str] = None) -> Dict[str, Any]:
        """统一缓存同步 - 仅同步文件缓存"""
        results = {"database_sync": {}, "file_sync": {}, "total_symbols": len(symbols) if symbols else 0, "success": False}

        try:
            results["database_sync"] = {"synced": 0, "skipped": 0, "errors": 0}

            # 同步文件缓存
            if cache_dir:
                results["file_sync"] = self.sync_with_file_cache(cache_dir, symbols)

            # 检查是否成功
            total_synced = results["database_sync"].get("synced", 0) + results["file_sync"].get("synced", 0)
            total_errors = results["database_sync"].get("errors", 0) + results["file_sync"].get("errors", 0)

            results["success"] = total_synced > 0 and total_errors == 0
            results["total_synced"] = total_synced
            results["total_errors"] = total_errors

            logger.info(f"Unified cache sync completed: {results}")

        except Exception as e:
            logger.error(f"Error in unified cache sync: {e}")
            results["error"] = str(e)

        return results

    def get_historical_data(self, symbol: str, period: str = "1y", interval: str = "1d") -> pd.DataFrame:
        """获取历史数据"""
        if not symbol or symbol.strip() == "":
            return pd.DataFrame()

        symbol = symbol.strip().upper()
        cache_key = self._get_cache_key(DataType.HISTORICAL, symbol, f"{period}_{interval}")

        # 检查缓存
        cached_data = self.get_multi_level(cache_key, DataType.HISTORICAL)
        if cached_data is not None:
            return cached_data

        # 从API获取数据
        try:
            import yfinance as yf

            ticker = yf.Ticker(symbol)
            df = ticker.history(period=period, interval=interval)

            if not df.empty:
                # 缓存数据
                self.set_multi_level(cache_key, df, DataType.HISTORICAL)
                return df
            else:
                logger.warning(f"No historical data found for {symbol}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()

    def get_current_prices(self, symbols: List[str]) -> Dict[str, float]:
        """获取当前价格"""
        if not symbols:
            return {}

        try:
            # 清理符号列表
            cleaned_symbols = []
            for symbol in symbols:
                cleaned_symbol = symbol.strip().replace('"', "").upper()
                cleaned_symbols.append(cleaned_symbol)

            prices = self._get_current_prices_sync(cleaned_symbols)
            return prices if prices is not None else {}
        except Exception as e:
            self.logger.error(f"Error getting current prices: {e}")
            return {}

    def _get_current_prices_sync(self, symbols: List[str]) -> Dict[str, float]:
        """同步获取当前价格"""
        try:
            return asyncio.run(self._get_current_prices_batch(symbols))
        except Exception as e:
            self.logger.error(f"Error in sync price fetch: {e}")
            return self._fallback_get_prices(symbols)

    async def _get_current_prices_batch(self, symbols: List[str]) -> Dict[str, float]:
        """批量获取当前价格 - 使用真正的Redis批量操作"""
        if not symbols:
            return {}

        prices = {}
        cache_misses = []

        # 首先检查L1缓存
        l1_misses = []
        for symbol in symbols:
            key = self._get_cache_key(DataType.PRICE, symbol)
            cached_price = self.l1_cache.get(key)
            if cached_price is not None:
                prices[symbol] = cached_price.get("price", 0.0)
                self.cache_stats["l1_hits"] += 1
            else:
                l1_misses.append((symbol, key))
                self.cache_stats["l1_misses"] += 1

        # 批量检查Redis缓存 (真正的批量操作)
        if l1_misses and self.redis_healthy and self.redis_client:
            try:
                keys = [item[1] for item in l1_misses]
                # 使用同步Redis操作
                redis_values = []
                for key in keys:
                    try:
                        value = self.redis_client.get(key)
                        redis_values.append(value)
                    except Exception:
                        redis_values.append(None)

                for (symbol, key), redis_data in zip(l1_misses, redis_values):
                    if redis_data:
                        try:
                            if self.config.compression_enabled:
                                cached_price = pickle.loads(redis_data)
                            else:
                                cached_price = json.loads(redis_data.decode("utf-8"))

                            prices[symbol] = cached_price.get("price", 0.0)
                            # 回填L1缓存
                            self.l1_cache.set(
                                key, cached_price, min(self._get_ttl_for_type(DataType.PRICE), self.config.l1_ttl)
                            )
                            self.cache_stats["l2_hits"] += 1
                        except Exception as e:
                            logger.error(f"Error deserializing cached price for {symbol}: {e}")
                            cache_misses.append(symbol)
                    else:
                        cache_misses.append(symbol)
                        self.cache_stats["l2_misses"] += 1

            except Exception as e:
                logger.error(f"Redis batch get error: {e}")
                self._handle_redis_error()
                # 如果Redis失败，所有L1未命中的都变成cache miss
                cache_misses.extend([item[0] for item in l1_misses])
        else:
            # Redis不可用，所有L1未命中的都变成cache miss
            cache_misses.extend([item[0] for item in l1_misses])

        # 获取缓存未命中的数据
        if cache_misses:
            fresh_prices = self._fetch_prices_batch(cache_misses)
            prices.update(fresh_prices)

            # 批量缓存新数据
            self._cache_prices_batch_optimized(fresh_prices)

        return prices

    def _fetch_prices_batch(self, symbols: List[str]) -> Dict[str, float]:
        """批量获取价格数据"""
        prices = {}

        def fetch_single_price(symbol):
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                price = info.get("regularMarketPrice") or info.get("currentPrice") or info.get("previousClose")
                if price and price > 0:
                    return symbol, float(price)
            except Exception as e:
                logger.error(f"Error fetching price for {symbol}: {e}")
                self.cache_stats["api_errors"] += 1  # 增加API错误计数
            return symbol, None

        # 并发获取价格
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            future_to_symbol = {executor.submit(fetch_single_price, symbol): symbol for symbol in symbols}

            for future in as_completed(future_to_symbol):
                symbol, price = future.result()
                if price is not None:
                    prices[symbol] = price

        return prices

    def _cache_prices_batch(self, prices: Dict[str, float]) -> None:
        """批量缓存价格数据 - 重定向到优化版本"""
        self._cache_prices_batch_optimized(prices)

    def _cache_prices_batch_optimized(self, prices: Dict[str, float]) -> None:
        """优化的批量缓存价格数据 - 使用Redis Pipeline"""
        if not prices:
            return

        current_time = time.time()
        ttl = self._get_ttl_for_type(DataType.PRICE)

        # 批量设置L1缓存
        for symbol, price in prices.items():
            key = self._get_cache_key(DataType.PRICE, symbol)
            cache_data = {"price": price, "timestamp": current_time}
            self.l1_cache.set(key, cache_data, min(ttl, self.config.l1_ttl))

        # 批量设置Redis缓存 (使用Pipeline)
        if self.redis_healthy and self.redis_client:
            try:
                pipe = self.redis_client.pipeline()
                for symbol, price in prices.items():
                    key = self._get_cache_key(DataType.PRICE, symbol)
                    cache_data = {"price": price, "timestamp": current_time}

                    if self.config.compression_enabled:
                        serialized_data = pickle.dumps(cache_data)
                    else:
                        serialized_data = json.dumps(cache_data, default=str).encode("utf-8")

                    pipe.setex(key, ttl, serialized_data)

                # 执行所有操作
                pipe.execute()

            except Exception as e:
                logger.error(f"Redis pipeline error in batch cache: {e}")
                self._handle_redis_error()

    def _fallback_get_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Redis不可用时的回退方案"""
        return self._fetch_prices_batch(symbols)

    def get_historical_data_batch(
        self, symbols: List[str], period: str = "1y", interval: str = "1d"
    ) -> Dict[str, pd.DataFrame]:
        """优化的批量获取历史数据 - 使用Redis批量操作"""
        if not symbols:
            return {}

        results = {}
        cache_misses = []

        # 清理和准备符号列表
        cleaned_symbols = []
        for symbol in symbols:
            if symbol and symbol.strip():
                cleaned_symbol = symbol.strip().upper()
                cleaned_symbols.append(cleaned_symbol)

        # 批量检查L1缓存
        l1_misses = []
        for symbol in cleaned_symbols:
            cache_key = self._get_cache_key(DataType.HISTORICAL, symbol, f"{period}_{interval}")
            cached_data = self.l1_cache.get(cache_key)
            if cached_data is not None:
                results[symbol] = cached_data
                self.cache_stats["l1_hits"] += 1
            else:
                l1_misses.append((symbol, cache_key))
                self.cache_stats["l1_misses"] += 1

        # 批量检查Redis缓存
        if l1_misses and self.redis_healthy and self.redis_client:
            try:
                keys = [item[1] for item in l1_misses]
                # 使用同步Redis操作
                redis_values = []
                for key in keys:
                    try:
                        value = self.redis_client.get(key)
                        redis_values.append(value)
                    except Exception:
                        redis_values.append(None)

                for (symbol, key), redis_data in zip(l1_misses, redis_values):
                    if redis_data:
                        try:
                            if self.config.compression_enabled:
                                df = pickle.loads(redis_data)
                            else:
                                df = pd.read_json(redis_data.decode("utf-8"))

                            results[symbol] = df
                            # 回填L1缓存
                            self.l1_cache.set(key, df, min(self._get_ttl_for_type(DataType.HISTORICAL), self.config.l1_ttl))
                            self.cache_stats["l2_hits"] += 1
                        except Exception as e:
                            logger.error(f"Error deserializing cached historical data for {symbol}: {e}")
                            cache_misses.append(symbol)
                    else:
                        cache_misses.append(symbol)
                        self.cache_stats["l2_misses"] += 1

            except Exception as e:
                logger.error(f"Redis batch get error for historical data: {e}")
                self._handle_redis_error()
                cache_misses.extend([item[0] for item in l1_misses])
        else:
            cache_misses.extend([item[0] for item in l1_misses])

        # 获取缓存未命中的数据
        if cache_misses:
            fresh_data = self._fetch_historical_data_batch(cache_misses, period, interval)
            results.update(fresh_data)

            # 批量缓存新数据
            self._cache_historical_data_batch(fresh_data, period, interval)

        return results

    def _fetch_historical_data_batch(
        self, symbols: List[str], period: str = "1y", interval: str = "1d"
    ) -> Dict[str, pd.DataFrame]:
        """批量获取历史数据 - 并发处理"""
        results = {}

        def fetch_single_historical(symbol):
            try:
                import yfinance as yf

                ticker = yf.Ticker(symbol)
                df = ticker.history(period=period, interval=interval)
                if not df.empty:
                    return symbol, df
                else:
                    logger.warning(f"No historical data found for {symbol}")
            except Exception as e:
                logger.error(f"Error fetching historical data for {symbol}: {e}")
                self.cache_stats["api_errors"] += 1
            return symbol, None

        # 并发获取历史数据
        with ThreadPoolExecutor(max_workers=min(self.config.max_workers, len(symbols))) as executor:
            future_to_symbol = {executor.submit(fetch_single_historical, symbol): symbol for symbol in symbols}

            for future in as_completed(future_to_symbol):
                symbol, df = future.result()
                if df is not None:
                    results[symbol] = df

        return results

    def _cache_historical_data_batch(self, data: Dict[str, pd.DataFrame], period: str = "1y", interval: str = "1d") -> None:
        """批量缓存历史数据 - 使用Redis Pipeline"""
        if not data:
            return

        ttl = self._get_ttl_for_type(DataType.HISTORICAL)

        # 批量设置L1缓存
        for symbol, df in data.items():
            cache_key = self._get_cache_key(DataType.HISTORICAL, symbol, f"{period}_{interval}")
            self.l1_cache.set(cache_key, df, min(ttl, self.config.l1_ttl))

        # 批量设置Redis缓存 (使用Pipeline)
        if self.redis_healthy and self.redis_client:
            try:
                pipe = self.redis_client.pipeline()
                for symbol, df in data.items():
                    cache_key = self._get_cache_key(DataType.HISTORICAL, symbol, f"{period}_{interval}")

                    if self.config.compression_enabled:
                        serialized_data = pickle.dumps(df)
                    else:
                        serialized_data = df.to_json().encode("utf-8")

                    pipe.setex(cache_key, ttl, serialized_data)

                # 执行所有操作
                pipe.execute()

            except Exception as e:
                logger.error(f"Redis pipeline error in historical data batch cache: {e}")
                self._handle_redis_error()

    def update_price(self, symbol: str, price: float) -> None:
        """更新股票价格"""
        key = self._get_cache_key(DataType.PRICE, symbol)
        cache_data = {"price": price, "timestamp": time.time()}
        self.set_multi_level(key, cache_data, DataType.PRICE)

    def is_cache_valid(self, symbol: str, data_type: str = "price") -> bool:
        """检查缓存是否有效"""
        try:
            dt = DataType(data_type.lower())
            key = self._get_cache_key(dt, symbol)
            cached_data = self.get_multi_level(key, dt)
            return cached_data is not None
        except ValueError:
            return False

    # 批量方法
    def get_portfolio_prices(self, symbols: List[str]) -> Dict[str, float]:
        """批量获取投资组合价格"""
        return self._get_current_prices_sync(symbols)

    def clear_cache(self):
        """清空所有缓存"""
        try:
            # 清空L1缓存
            self.l1_cache.clear()

            # 清空Redis缓存
            if self.redis_healthy and self.redis_client:
                try:
                    # 简单清理方案 - 使用flushdb清空当前数据库
                    self.redis_client.flushdb()
                    logger.info("Redis cache cleared successfully")
                except Exception as e:
                    logger.error(f"Error clearing Redis cache: {e}")

            logger.info("Cache cleared successfully")
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")

    def shutdown(self):
        """关闭缓存系统"""
        try:
            if hasattr(self, "executor") and self.executor:
                self.executor.shutdown(wait=True)

            if hasattr(self, "redis_client") and self.redis_client:
                try:
                    self.redis_client.close()
                except:
                    pass

            logger.info("Stock cache system shutdown completed")
        except Exception as e:
            logger.error(f"Error during cache shutdown: {e}")


# 创建全局缓存实例
stock_cache = StockCache()


# 清理函数
def _cleanup_cache():
    """清理缓存资源"""
    try:
        if "stock_cache" in globals():
            stock_cache.shutdown()
    except Exception as e:
        logger.error(f"Error during cache cleanup: {e}")


# 注册信号处理器
def _signal_handler(signum, _frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down cache...")
    _cleanup_cache()


# 注册信号处理
signal.signal(signal.SIGTERM, _signal_handler)
signal.signal(signal.SIGINT, _signal_handler)

# 注册退出处理
atexit.register(_cleanup_cache)

# 全局缓存实例
_stock_cache_instance = None


def get_stock_cache() -> StockCache:
    """获取全局股票缓存实例"""
    global _stock_cache_instance
    if _stock_cache_instance is None:
        _stock_cache_instance = StockCache()
    return _stock_cache_instance


# 创建全局实例
stock_cache = get_stock_cache()
