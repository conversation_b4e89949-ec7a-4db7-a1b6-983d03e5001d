"""
 Win Rate Scoring System

This module provides mathematically rigorous win rate scoring algorithms that replace
the current composite scoring approach with proper probability-based calculations.
"""

import math
from typing import Dict, Optional

import numpy as np
import pandas as pd

from src.utils.logger import get_logger

from .options_probability import OptionsprobabilityCalculator

logger = get_logger()


class WinRateScorer:
    """Win rate scoring system for options strategies."""

    def __init__(self, risk_free_rate: float = 0.05):
        """
        Initialize the win rate scorer.

        Args:
            risk_free_rate: Risk-free interest rate for calculations
        """
        self.prob_calculator = OptionsprobabilityCalculator(risk_free_rate)
        self.risk_free_rate = risk_free_rate

    def calculate_put_win_rate_score(self, df: pd.DataFrame, market_conditions: Optional[Dict] = None) -> pd.Series:
        """
        Enhanced win rate score calculation for cash-secured puts using multiple probability methods.

        Args:
            df: DataFrame with put option data
            market_conditions: Optional market condition data

        Returns:
            Series of enhanced win rate scores (0-100)
        """
        try:
            scores = pd.Series(index=df.index, dtype=float)

            for idx, row in df.iterrows():
                # Extract required data
                current_price = row.get("currentPrice", 0)
                strike = row.get("strike", 0)
                dte = row.get("dte", 30)
                iv = row.get("impliedVolatility", 0.25)
                delta = row.get("delta", None)

                if current_price <= 0 or strike <= 0:
                    scores[idx] = 0.0
                    continue

                time_to_expiry = dte / 365.0

                # 1. Primary Probability Score (50% weight)
                if delta is not None:
                    # Use delta-based probability if available
                    prob_profit = self.prob_calculator.delta_based_probability(delta, "put")
                else:
                    # Fall back to Black-Scholes probability
                    prob_profit = self.prob_calculator.black_scholes_probability(
                        current_price, strike, time_to_expiry, iv, "put"
                    )

                # Apply time decay and market regime adjustments
                prob_profit = self.prob_calculator.time_decay_adjustment(prob_profit, dte, "income")
                prob_profit = self.prob_calculator.market_regime_adjustment(prob_profit, iv, "income")

                probability_score = prob_profit * 100

                # 2. Risk-Adjusted Return Score (25% weight)
                buffer_percent = row.get("bufferPercent", 0)
                premium = row.get("bid", 0) * 100  # Premium per contract
                max_loss = (strike * 100) - premium

                if max_loss > 0:
                    risk_adjusted_return = (premium / max_loss) * math.sqrt(prob_profit)
                    return_score = min(100, risk_adjusted_return * 200)  # Scale to 0-100
                else:
                    return_score = 0

                # 3. Liquidity and Execution Score (15% weight)
                bid = row.get("bid", 0)
                ask = row.get("ask", 0)
                volume = row.get("volume", 0)
                open_interest = row.get("openInterest", 0)

                if ask > 0 and bid > 0:
                    spread_ratio = (ask - bid) / ((ask + bid) / 2)
                    spread_score = max(0, 100 - spread_ratio * 500)  # Penalize wide spreads
                else:
                    spread_score = 0

                volume_score = min(100, volume / 10)  # Scale volume
                oi_score = min(100, open_interest / 50)  # Scale open interest
                liquidity_score = spread_score * 0.5 + volume_score * 0.25 + oi_score * 0.25

                # 4. Time Efficiency Score (10% weight)
                optimal_dte = 37.5
                time_efficiency = 100 - abs(dte - optimal_dte) / optimal_dte * 100
                time_efficiency = max(0, min(100, time_efficiency))

                # Apply volatility-based time adjustment
                if iv > 0.5:  # High volatility - prefer shorter time
                    optimal_dte = 30
                elif iv < 0.25:  # Low volatility - can use longer time
                    optimal_dte = 45

                time_efficiency = 100 - abs(dte - optimal_dte) / optimal_dte * 100
                time_efficiency = max(0, min(100, time_efficiency))

                # 5. Composite Score Calculation
                weights = {"probability": 0.50, "risk_return": 0.25, "liquidity": 0.15, "time": 0.10}

                composite_score = (
                    probability_score * weights["probability"]
                    + return_score * weights["risk_return"]
                    + liquidity_score * weights["liquidity"]
                    + time_efficiency * weights["time"]
                )

                scores[idx] = round(composite_score, 1)

            return scores

        except Exception as e:
            logger.error(f"Error in enhanced put win rate calculation: {e}")
            return pd.Series([50.0] * len(df), index=df.index)

    def calculate_call_win_rate_score(self, df: pd.DataFrame, market_conditions: Optional[Dict] = None) -> pd.Series:
        """
        Enhanced win rate score calculation for covered calls.

        Args:
            df: DataFrame with call option data
            market_conditions: Optional market condition data

        Returns:
            Series of enhanced win rate scores (0-100)
        """
        try:
            scores = pd.Series(index=df.index, dtype=float)

            for idx, row in df.iterrows():
                # Extract required data
                current_price = row.get("currentPrice", 0)
                strike = row.get("strike", 0)
                dte = row.get("dte", 30)
                iv = row.get("impliedVolatility", 0.25)
                delta = row.get("delta", None)

                if current_price <= 0 or strike <= 0:
                    scores[idx] = 0.0
                    continue

                time_to_expiry = dte / 365.0

                # 1. Primary Probability Score (45% weight)
                if delta is not None:
                    prob_profit = self.prob_calculator.delta_based_probability(delta, "call")
                else:
                    prob_profit = self.prob_calculator.black_scholes_probability(
                        current_price, strike, time_to_expiry, iv, "call"
                    )

                # Apply adjustments
                prob_profit = self.prob_calculator.time_decay_adjustment(prob_profit, dte, "income")
                prob_profit = self.prob_calculator.market_regime_adjustment(prob_profit, iv, "income")

                probability_score = prob_profit * 100

                # 2. Total Return Potential Score (30% weight)
                premium = row.get("bid", 0) * 100
                upside_potential = max(0, (strike - current_price) * 100)
                total_return = premium + upside_potential

                if current_price > 0:
                    total_return_pct = total_return / (current_price * 100)
                    annualized_return = total_return_pct * (365 / dte)
                    return_score = min(100, annualized_return * 500)  # Scale to 0-100
                else:
                    return_score = 0

                # 3. Assignment Risk Score (15% weight)
                upside_buffer = row.get("upsideBufferPercent", 0)
                assignment_risk_score = min(100, upside_buffer * 1000)  # Higher buffer = higher score

                # 4. Liquidity Score (10% weight)
                bid = row.get("bid", 0)
                ask = row.get("ask", 0)
                volume = row.get("volume", 0)

                if ask > 0 and bid > 0:
                    spread_ratio = (ask - bid) / ((ask + bid) / 2)
                    liquidity_score = max(0, 100 - spread_ratio * 500)
                else:
                    liquidity_score = 0

                # 5. Composite Score
                weights = {"probability": 0.45, "return": 0.30, "assignment_risk": 0.15, "liquidity": 0.10}

                composite_score = (
                    probability_score * weights["probability"]
                    + return_score * weights["return"]
                    + assignment_risk_score * weights["assignment_risk"]
                    + liquidity_score * weights["liquidity"]
                )

                scores[idx] = round(composite_score, 1)

            return scores

        except Exception as e:
            logger.error(f"Error in enhanced call win rate calculation: {e}")
            return pd.Series([50.0] * len(df), index=df.index)

    def calculate_iron_condor_win_rate_score(self, df: pd.DataFrame, market_conditions: Optional[Dict] = None) -> pd.Series:
        """
        Enhanced win rate score calculation for iron condors using advanced probability methods.

        Args:
            df: DataFrame with iron condor data
            market_conditions: Optional market condition data

        Returns:
            Series of enhanced win rate scores (0-100)
        """
        try:
            scores = pd.Series(index=df.index, dtype=float)

            for idx, row in df.iterrows():
                # Extract required data
                current_price = row.get("currentPrice", 0)
                short_put_strike = row.get("shortPutStrike", 0)
                short_call_strike = row.get("shortCallStrike", 0)
                dte = row.get("dte", 30)
                iv = row.get("impliedVolatility", 0.25)

                if current_price <= 0 or short_put_strike <= 0 or short_call_strike <= 0:
                    scores[idx] = 0.0
                    continue

                time_to_expiry = dte / 365.0

                # 1. Advanced Probability Score (60% weight)
                prob_results = self.prob_calculator.iron_condor_probability_advanced(
                    current_price, short_put_strike, short_call_strike, time_to_expiry, iv, monte_carlo_sims=5000
                )

                base_prob = prob_results.get("composite", 0.5)

                # Apply time decay and market regime adjustments
                adjusted_prob = self.prob_calculator.time_decay_adjustment(base_prob, dte, "neutral")
                adjusted_prob = self.prob_calculator.market_regime_adjustment(adjusted_prob, iv, "neutral")

                probability_score = adjusted_prob * 100

                # 2. Risk-Reward Efficiency Score (25% weight)
                max_profit = row.get("maxProfit", 0)
                max_loss = row.get("maxLoss", 0)

                if max_loss > 0:
                    risk_reward_ratio = max_profit / max_loss
                    # Weight by probability for risk-adjusted return
                    risk_adjusted_efficiency = risk_reward_ratio * adjusted_prob
                    efficiency_score = min(100, risk_adjusted_efficiency * 300)
                else:
                    efficiency_score = 0

                # 3. Structure Balance Score (10% weight)
                put_width = row.get("putWidth", 0)
                call_width = row.get("callWidth", 0)

                if put_width > 0 and call_width > 0:
                    balance_ratio = min(put_width, call_width) / max(put_width, call_width)
                    balance_score = balance_ratio * 100
                else:
                    balance_score = 50  # Neutral if can't calculate

                # 4. Market Position Score (5% weight)
                # Score based on how well-centered the condor is
                condor_center = (short_put_strike + short_call_strike) / 2
                center_distance = abs(current_price - condor_center) / current_price
                position_score = max(0, 100 - center_distance * 500)

                # 5. Composite Score
                weights = {"probability": 0.60, "efficiency": 0.25, "balance": 0.10, "position": 0.05}

                composite_score = (
                    probability_score * weights["probability"]
                    + efficiency_score * weights["efficiency"]
                    + balance_score * weights["balance"]
                    + position_score * weights["position"]
                )

                scores[idx] = round(composite_score, 1)

            return scores

        except Exception as e:
            logger.error(f"Error in enhanced iron condor win rate calculation: {e}")
            return pd.Series([50.0] * len(df), index=df.index)
