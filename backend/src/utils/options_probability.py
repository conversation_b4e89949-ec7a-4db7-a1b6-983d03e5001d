"""
Options Probability Calculations

This module provides mathematically rigorous probability calculations for options strategies,
replacing the simplified heuristics with proper Black-Scholes framework, Monte Carlo
simulations, and delta-based probability estimates.
"""

import math
from typing import Dict

import numpy as np
from scipy.stats import norm

from src.utils.logger import get_logger

logger = get_logger()


class OptionsprobabilityCalculator:
    """Advanced probability calculator for options strategies."""

    def __init__(self, risk_free_rate: float = 0.05):
        """
        Initialize the probability calculator.

        Args:
            risk_free_rate: Risk-free interest rate (default 5%)
        """
        self.risk_free_rate = risk_free_rate

    def black_scholes_probability(
        self, current_price: float, strike: float, time_to_expiry: float, volatility: float, option_type: str = "put"
    ) -> float:
        """
        Calculate probability of profit using Black-Scholes framework.

        For puts: P(S_T < K) where S_T is stock price at expiration
        For calls: P(S_T > K)

        Args:
            current_price: Current stock price
            strike: Option strike price
            time_to_expiry: Time to expiration in years
            volatility: Implied volatility
            option_type: "put" or "call"

        Returns:
            Probability of profit (0.0 to 1.0)
        """
        try:
            if time_to_expiry <= 0 or volatility <= 0:
                return 0.5  # Default for edge cases

            # Calculate d2 from Black-Scholes
            d2 = (math.log(current_price / strike) + (self.risk_free_rate - 0.5 * volatility**2) * time_to_expiry) / (
                volatility * math.sqrt(time_to_expiry)
            )

            if option_type.lower() == "put":
                # Probability that stock price < strike at expiration
                return norm.cdf(-d2)
            else:  # call
                # Probability that stock price > strike at expiration
                return norm.cdf(d2)

        except Exception as e:
            logger.warning(f"Error in Black-Scholes probability calculation: {e}")
            return 0.5

    def delta_based_probability(self, delta: float, option_type: str = "put") -> float:
        """
        Estimate probability of profit using delta approximation.

        Delta approximates the probability of finishing in-the-money.
        For profit calculation, we need probability of finishing out-of-the-money.

        Args:
            delta: Option delta
            option_type: "put" or "call"

        Returns:
            Probability of profit (0.0 to 1.0)
        """
        try:
            if option_type.lower() == "put":
                # Put delta is negative, probability of profit = 1 + delta
                return max(0.0, min(1.0, 1.0 + delta))
            else:  # call
                # Call delta is positive, probability of profit = 1 - delta
                return max(0.0, min(1.0, 1.0 - delta))

        except Exception as e:
            logger.warning(f"Error in delta-based probability calculation: {e}")
            return 0.5

    def iron_condor_probability_advanced(
        self,
        current_price: float,
        short_put_strike: float,
        short_call_strike: float,
        time_to_expiry: float,
        volatility: float,
        monte_carlo_sims: int = 10000,
    ) -> Dict[str, float]:
        """
        Calculate iron condor probability of profit using multiple methods.

        Args:
            current_price: Current stock price
            short_put_strike: Short put strike price
            short_call_strike: Short call strike price
            time_to_expiry: Time to expiration in years
            volatility: Implied volatility
            monte_carlo_sims: Number of Monte Carlo simulations

        Returns:
            Dictionary with different probability estimates
        """
        try:
            results = {}

            # Method 1: Black-Scholes based
            prob_above_put = self.black_scholes_probability(
                current_price, short_put_strike, time_to_expiry, volatility, "call"
            )
            prob_below_call = self.black_scholes_probability(
                current_price, short_call_strike, time_to_expiry, volatility, "put"
            )
            results["black_scholes"] = prob_above_put + prob_below_call - 1.0
            results["black_scholes"] = max(0.0, min(1.0, results["black_scholes"]))

            # Method 2: Normal distribution approximation
            expected_move = current_price * volatility * math.sqrt(time_to_expiry)
            lower_bound = short_put_strike
            upper_bound = short_call_strike

            # Probability that final price is between strikes
            z_lower = (lower_bound - current_price) / expected_move
            z_upper = (upper_bound - current_price) / expected_move
            prob_between = norm.cdf(z_upper) - norm.cdf(z_lower)
            results["normal_distribution"] = prob_between

            # Method 3: Monte Carlo simulation (simplified)
            if monte_carlo_sims > 0:
                results["monte_carlo"] = self._monte_carlo_iron_condor(
                    current_price, short_put_strike, short_call_strike, time_to_expiry, volatility, monte_carlo_sims
                )

            # Method 4: Composite estimate (weighted average)
            weights = {"black_scholes": 0.4, "normal_distribution": 0.4, "monte_carlo": 0.2}
            composite = sum(results.get(method, 0.5) * weight for method, weight in weights.items() if method in results)
            results["composite"] = composite

            return results

        except Exception as e:
            logger.warning(f"Error in advanced iron condor probability calculation: {e}")
            return {"composite": 0.5}

    def _monte_carlo_iron_condor(
        self,
        current_price: float,
        short_put_strike: float,
        short_call_strike: float,
        time_to_expiry: float,
        volatility: float,
        num_sims: int,
    ) -> float:
        """
        Monte Carlo simulation for iron condor probability of profit.

        Args:
            current_price: Current stock price
            short_put_strike: Short put strike price
            short_call_strike: Short call strike price
            time_to_expiry: Time to expiration in years
            volatility: Implied volatility
            num_sims: Number of simulations

        Returns:
            Probability of profit from Monte Carlo simulation
        """
        try:
            # Generate random price movements using geometric Brownian motion
            dt = time_to_expiry
            drift = (self.risk_free_rate - 0.5 * volatility**2) * dt
            diffusion = volatility * math.sqrt(dt)

            # Generate random normal variables
            random_moves = np.random.normal(0, 1, num_sims)

            # Calculate final stock prices
            final_prices = current_price * np.exp(drift + diffusion * random_moves)

            # Count profitable outcomes (price between strikes)
            profitable = np.sum((final_prices >= short_put_strike) & (final_prices <= short_call_strike))

            return profitable / num_sims

        except Exception as e:
            logger.warning(f"Error in Monte Carlo simulation: {e}")
            return 0.5

    def time_decay_adjustment(self, base_probability: float, days_to_expiry: int, strategy_type: str = "neutral") -> float:
        """
        Adjust probability based on time decay effects.

        Args:
            base_probability: Base probability estimate
            days_to_expiry: Days to expiration
            strategy_type: "neutral", "directional", or "income"

        Returns:
            Time-adjusted probability
        """
        try:
            # Time decay factor based on strategy type
            if strategy_type == "neutral":
                # Neutral strategies benefit from time decay
                time_factor = 1.0 + (0.1 * (45 - days_to_expiry) / 45)
            elif strategy_type == "income":
                # Income strategies benefit significantly from time decay
                time_factor = 1.0 + (0.15 * (45 - days_to_expiry) / 45)
            else:  # directional
                # Directional strategies hurt by time decay
                time_factor = 1.0 - (0.05 * (45 - days_to_expiry) / 45)

            # Apply bounds
            time_factor = max(0.8, min(1.2, time_factor))
            adjusted_prob = base_probability * time_factor

            return max(0.0, min(1.0, adjusted_prob))

        except Exception as e:
            logger.warning(f"Error in time decay adjustment: {e}")
            return base_probability

    def market_regime_adjustment(
        self, base_probability: float, implied_volatility: float, strategy_type: str = "neutral"
    ) -> float:
        """
        Adjust probability based on market regime (volatility environment).

        Args:
            base_probability: Base probability estimate
            implied_volatility: Current implied volatility
            strategy_type: Strategy type for regime-specific adjustments

        Returns:
            Market regime adjusted probability
        """
        try:
            # Define volatility regimes
            if implied_volatility < 0.20:
                regime = "low_vol"
                regime_factor = 0.95  # Slightly lower success in low vol
            elif implied_volatility < 0.35:
                regime = "normal_vol"
                regime_factor = 1.0  # Baseline
            elif implied_volatility < 0.50:
                regime = "high_vol"
                regime_factor = 1.05  # Slightly higher success in high vol
            else:
                regime = "extreme_vol"
                regime_factor = 0.90  # Lower success in extreme vol

            # Strategy-specific adjustments
            if strategy_type == "neutral" and regime in ["high_vol", "extreme_vol"]:
                regime_factor *= 1.1  # Neutral strategies benefit from high vol
            elif strategy_type == "income" and regime == "low_vol":
                regime_factor *= 0.9  # Income strategies struggle in low vol

            adjusted_prob = base_probability * regime_factor
            return max(0.0, min(1.0, adjusted_prob))

        except Exception as e:
            logger.warning(f"Error in market regime adjustment: {e}")
            return base_probability
