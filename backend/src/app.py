import atexit
import os

from dotenv import load_dotenv
from flask import Flask, request
from flask_cors import CORS

from .api import accounts_bp, analytics_bp, market_bp, strategies_bp, transactions_bp
from .core.scheduler import start_scheduler, stop_scheduler
from .utils.cache import stock_cache

# 加载环境变量
load_dotenv()

app = Flask(__name__)
app.url_map.strict_slashes = False

# 获取环境变量，如果不存在则使用默认值
ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:5173").split(",")
CORS_MAX_AGE = int(os.getenv("CORS_MAX_AGE", "600"))

# 配置 CORS
CORS(
    app,
    origins=ALLOWED_ORIGINS,
    methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
    expose_headers=["Content-Type", "Authorization"],
    supports_credentials=True,
    max_age=CORS_MAX_AGE,
)


# 处理 OPTIONS 请求
@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = app.make_default_options_response()
        # 确保预检请求的响应中包含正确的 CORS 头
        response.headers["Access-Control-Allow-Origin"] = request.headers.get("Origin", "*")
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, Accept, Origin"
        response.headers["Access-Control-Max-Age"] = str(CORS_MAX_AGE)
        response.headers["Access-Control-Allow-Credentials"] = "true"
        return response


# Stock cache is imported and ready to use

# 标记是否已初始化调度器
scheduler_initialized = False


# 在每个请求之前检查调度器是否已初始化
@app.before_request
def initialize_scheduler():
    global scheduler_initialized
    if not scheduler_initialized:
        start_scheduler()
        scheduler_initialized = True


# 确保在应用关闭时停止调度器
@atexit.register
def cleanup():
    stop_scheduler()


# 注册蓝图
app.register_blueprint(accounts_bp)
app.register_blueprint(transactions_bp)
app.register_blueprint(analytics_bp)
app.register_blueprint(market_bp)
app.register_blueprint(strategies_bp)

if __name__ == "__main__":
    app.run(debug=True, port=5001)
