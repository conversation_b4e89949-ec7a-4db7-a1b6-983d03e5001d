import atexit
import os
import sqlite3
import sys
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path

import schedule
from exchange_calendars import get_calendar

from ..utils.cache import get_stock_cache
from ..utils.logger import get_logger, log_exceptions

# Ensure the backend directory is in the Python path to resolve model imports
BACKEND_PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if BACKEND_PROJECT_ROOT not in sys.path:
    sys.path.append(BACKEND_PROJECT_ROOT)

from ..models.prediction_model.prediction_pipeline import run_training_pipeline_for_tickers


class StockDataManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *_args, **_kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance

    @log_exceptions
    def __init__(self, db_path=None, cache_dir=None):
        if not hasattr(self, "initialized"):
            if db_path is None:
                self.db_path = os.path.join(BACKEND_PROJECT_ROOT, "..", "data", "stock_trading.db")
            else:
                self.db_path = db_path
            if cache_dir is None:
                cache_dir = os.path.join(BACKEND_PROJECT_ROOT, "..", "data", "cache")
            self.cache_dir = Path(cache_dir)
            self.cache_dir.mkdir(exist_ok=True)
            self.scheduler_thread = None
            self.stop_flag = threading.Event()
            self.initialized = True
            self.logger = get_logger("StockDataManager")
            self.logger.info(f"Initialized StockDataManager with DB: {self.db_path}, Cache: {self.cache_dir}")
            self.model_retrainer = ModelRetrainingScheduler(db_path=self.db_path, logger_instance=self.logger)

            # 初始化统一缓存系统
            self.unified_cache = get_stock_cache()
            self._sync_caches_on_startup()

    def _sync_caches_on_startup(self):
        """启动时同步所有缓存系统"""
        try:
            self.logger.info("Starting unified cache synchronization...")
            result = self.unified_cache.unified_cache_sync(cache_dir=str(self.cache_dir))
            if result.get("success", False):
                self.logger.info(f"Cache sync successful: {result.get('total_synced', 0)} symbols synced")
            else:
                self.logger.warning(f"Cache sync completed with issues: {result}")
        except Exception as e:
            self.logger.error(f"Error during cache synchronization: {e}")

    def get_db_connection(self):
        return sqlite3.connect(self.db_path)

    def get_all_tickers(self, conn=None):
        """从数据库获取所有唯一的股票代码，排除0持仓的股票"""
        close_conn = False
        if conn is None:
            conn = self.get_db_connection()
            close_conn = True

        try:
            cursor = conn.execute(
                """
                SELECT DISTINCT symbol
                FROM transactions
                WHERE symbol != 'CASH'
                GROUP BY symbol
                HAVING SUM(quantity) > 1e-9
            """
            )
            # Clean symbols by removing spaces and special characters
            return [row[0].strip().replace('"', "") for row in cursor.fetchall()]
        finally:
            if close_conn:
                conn.close()

    def should_update_cache(self, symbol):
        """检查是否需要更新缓存文件（基于时间）"""
        cache_file = self.cache_dir / f"{symbol}_data.parquet"

        # 如果文件不存在，需要更新
        if not cache_file.exists():
            self.logger.info(f"Cache file for {symbol} does not exist")
            return True

        # 如果文件存在但超过12小时，需要更新
        file_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
        if datetime.now() - file_time > timedelta(hours=12):
            self.logger.info(f"Cache file for {symbol} is older than 12 hours")
            return True

        self.logger.debug(f"Cache file for {symbol} is up to date.")
        return False

    def get_latest_db_price(self, symbol: str, conn=None) -> tuple[str | None, float | None]:
        """获取数据库中特定股票的最新缓存价格和日期"""
        close_conn = False
        if conn is None:
            conn = self.get_db_connection()
            close_conn = True

        try:
            cursor = conn.execute(
                """
                SELECT date, close
                FROM cached_prices
                WHERE symbol = ?
                ORDER BY date DESC
                LIMIT 1
            """,
                (symbol,),
            )
            result = cursor.fetchone()
            if result:
                return result[0], result[1]
            else:
                return None, None
        except Exception as e:
            self.logger.error(f"Error fetching latest DB price for {symbol}: {str(e)}")
            return None, None
        finally:
            if close_conn:
                conn.close()

    def check_cache_health(self):
        """检查缓存系统健康状态 - 替代原有的价格检查功能"""
        self.logger.info("Running cache health check...")
        try:
            # 检查Redis缓存健康状态
            cache_health = self.unified_cache.get_cache_health()

            if cache_health.get("redis_healthy", False):
                self.logger.info("Cache system is healthy - Redis operational")
            else:
                self.logger.warning("Redis cache is not healthy, using L1 cache only")

            # 检查缓存中的数据量
            cache_stats = cache_health.get("stats", {})
            self.logger.info(f"Cache statistics: {cache_stats}")

        except Exception as e:
            self.logger.error(f"Error during cache health check: {str(e)}", exc_info=True)

    def start_scheduler(self):
        """启动调度器线程"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.logger.info("Scheduler is already running")
            return

        self.stop_flag.clear()
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("Scheduler thread started")

        # 注册退出时的清理函数
        atexit.register(self.stop_scheduler)

    def stop_scheduler(self):
        """停止调度器线程"""
        if self.scheduler_thread:
            self.logger.info("Stopping scheduler...")
            self.stop_flag.set()
            self.scheduler_thread.join(timeout=5)
            self.logger.info("Scheduler stopped")

    def _run_job_in_thread(self, job_func, *args, **kwargs):
        """Helper to run a job in a new daemon thread."""
        job_name = getattr(job_func, "__name__", "unknown_job")
        self.logger.info(f"Starting job '{job_name}' in a background thread.")
        job_thread = threading.Thread(target=job_func, args=args, kwargs=kwargs, daemon=True)
        job_thread.start()

    def _run_scheduler(self):
        """调度器线程的运行函数"""
        self.logger.info("Starting scheduler jobs...")

        # Cache health check job (替代原有的价格检查)
        self._run_job_in_thread(self.check_cache_health)
        schedule.every(4).hours.do(self._run_job_in_thread, self.check_cache_health)

        # Daily model retraining job
        self.logger.info("Scheduling daily model retraining job at 02:00 server time.")
        schedule.every().day.at("02:00").do(self._run_job_in_thread, self.model_retrainer.run_daily_model_retraining_job)
        while not self.stop_flag.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"Scheduler error: {str(e)}", exc_info=True)
                # Avoid busy-waiting on error, but make it interruptible
                if not self.stop_flag.is_set():
                    self.stop_flag.wait(timeout=30)


# --- Model Retraining Scheduler Class ---
class ModelRetrainingScheduler:
    def __init__(self, db_path: str, logger_instance):
        self.db_path = db_path
        self.logger = logger_instance if logger_instance else get_logger("ModelRetrainingScheduler")
        self.logger.info(f"Initialized ModelRetrainingScheduler with DB: {self.db_path}")

    def get_db_connection(self):
        return sqlite3.connect(self.db_path)

    def get_active_position_tickers(self) -> list:
        """
        Fetches all unique stock symbols where users currently have a position greater than zero.
        """
        tickers = set()
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            query = """
                SELECT DISTINCT symbol
                FROM transactions
                WHERE symbol != 'CASH'
                GROUP BY account_id, symbol
                HAVING SUM(quantity) > 1e-9;
            """
            cursor.execute(query)
            rows = cursor.fetchall()
            for row in rows:
                # Ensure symbol is a string and clean it
                symbol_val = row[0]
                if isinstance(symbol_val, str):
                    tickers.add(symbol_val.strip().replace('"', ""))
                else:
                    self.logger.warning(f"Non-string symbol found in DB: {symbol_val}")

            self.logger.info(
                f"Found {len(tickers)} unique tickers with active positions for model retraining: {list(tickers)}"
            )
        except Exception as e:
            self.logger.error(f"Error fetching active position tickers for retraining: {e}", exc_info=True)
        finally:
            if conn:
                conn.close()
        return list(tickers)

    @log_exceptions
    def run_daily_model_retraining_job(self, ticker_symbols_to_process=None):
        self.logger.info("--- Starting Daily Model Retraining Job via Scheduler ---")

        if ticker_symbols_to_process is None:
            active_tickers = self.get_active_position_tickers()
        else:
            active_tickers = ticker_symbols_to_process

        if not active_tickers:
            self.logger.info("No active tickers found in user holdings. Skipping daily model retraining.")
            return

        self.logger.info(f"Proceeding to retrain models for {len(active_tickers)} tickers: {active_tickers}")

        try:
            # This function is imported from models.prediction_model.prediction_pipeline
            run_training_pipeline_for_tickers(ticker_symbols_to_process=active_tickers, force_retrain=True)
            self.logger.info("--- Successfully completed Daily Model Retraining Job via Scheduler ---")
        except Exception as e:
            self.logger.error(f"Daily Model Retraining Job failed: {e}", exc_info=True)
            # Don't re-raise, allow scheduler to continue other jobs


def is_market_open_today(calendar_name="NYSE"):
    """
    Check if the market for a given calendar (e.g., NYSE) is open today.
    Uses the is_session() method to avoid timezone comparison issues.
    """
    nyse = get_calendar(calendar_name)
    today_str = datetime.now().strftime("%Y-%m-%d")
    return nyse.is_session(today_str)


stock_data_manager = StockDataManager()


def start_scheduler():
    """启动调度器的便捷函数"""
    stock_data_manager.start_scheduler()


def stop_scheduler():
    """停止调度器的便捷函数"""
    stock_data_manager.stop_scheduler()


if __name__ == "__main__":
    logger = get_logger("SchedulerMain")  # Logger for the __main__ block
    logger.info("Starting scheduler script directly.")
    start_scheduler()
    try:
        while True:
            time.sleep(60)
            logger.debug("Main scheduler script thread still alive...")
    except KeyboardInterrupt:
        logger.info("Scheduler script interrupted by user. Stopping...")
    finally:
        stop_scheduler()
        logger.info("Scheduler script finished.")
