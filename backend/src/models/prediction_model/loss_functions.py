"""
Loss Functions for Stock Trading Prediction Models

This module contains all loss functions used in the stock trading prediction system,
including QuantileLoss, MADLLoss (Mean Absolute Directional Loss), and HybridLoss.
"""

from typing import Union

import numpy as np
import torch
import torch.nn as nn


class QuantileLoss(nn.Module):
    """Pinball Loss Function for quantile regression."""

    def __init__(self, quantiles):
        super().__init__()
        self.quantiles = torch.tensor(quantiles, dtype=torch.float32)

    def forward(self, preds, target):
        # preds: (batch_size, seq_len, num_quantiles)
        # target: (batch_size, seq_len)
        if self.quantiles.device != preds.device:
            self.quantiles = self.quantiles.to(preds.device)

        target_unsqueezed = target.unsqueeze(-1)  # Shape: (batch_size, seq_len, 1)
        errors = target_unsqueezed - preds  # Broadcasts to (batch_size, seq_len, num_quantiles)

        q_tensor = self.quantiles.view(1, 1, -1)  # Shape: (1, 1, num_quantiles)
        loss = torch.max((q_tensor - 1) * errors, q_tensor * errors)

        return loss.mean()


class MADLLoss(nn.Module):
    """
    Mean Absolute Directional Loss (MADL) Function using Generalized MADL (GMADL) formula.

    GMADL = (1/N) * Σ(-1) * (1/(1+exp(-α*Ri*R̂i)) - 0.5) * |Ri|^β

    This loss function optimizes for directional accuracy weighted by return magnitude,
    making it more suitable for algorithmic trading strategies than traditional MSE/MAE.

    Args:
        alpha (float): Controls the slope around zero for differentiability. Higher values
                      create steeper transitions. Default: 1000.0
        beta (float): Controls return magnitude weighting. Higher values emphasize
                     larger returns more strongly. Default: 1.0
    """

    def __init__(self, alpha: float = 1000.0, beta: float = 1.0):
        super().__init__()
        self.alpha = alpha
        self.beta = beta

        # Validate parameters
        if alpha <= 0:
            raise ValueError("Alpha parameter must be positive")
        if beta <= 0:
            raise ValueError("Beta parameter must be positive")

    def forward(self, preds, target):
        """
        Forward pass of MADL loss.

        Args:
            preds: Predicted returns (batch_size, seq_len) or (batch_size, seq_len, num_quantiles)
            target: Actual returns (batch_size, seq_len)

        Returns:
            torch.Tensor: MADL loss value
        """
        # Handle quantile predictions by using median quantile
        if preds.dim() == 3:
            # For quantile predictions, use median quantile for directional loss
            median_idx = preds.size(-1) // 2
            preds = preds[:, :, median_idx]

        # Ensure preds and target have same shape
        if preds.dim() != target.dim():
            raise ValueError(f"Prediction and target dimensions must match. Got {preds.dim()} and {target.dim()}")

        # Calculate directional product (Ri * R̂i)
        directional_product = target * preds

        # Apply sigmoid transformation for differentiability: 1/(1+exp(-α*Ri*R̂i)) - 0.5
        sigmoid_term = torch.sigmoid(self.alpha * directional_product) - 0.5

        # Apply magnitude weighting: |Ri|^β
        magnitude_weight = torch.abs(target) ** self.beta

        # Calculate GMADL: (-1) * sigmoid_term * magnitude_weight
        loss = -sigmoid_term * magnitude_weight

        return loss.mean()


class HybridLoss(nn.Module):
    """
    Hybrid Loss combining QuantileLoss with MADLLoss for balanced optimization.

    This loss function combines the uncertainty quantification benefits of quantile
    regression with the directional optimization benefits of MADL, allowing models
    to optimize for both forecast accuracy and trading profitability.

    Args:
        quantiles (list): Quantile levels for QuantileLoss
        madl_weight (float): Weight for MADL component (0.0 to 1.0). Default: 0.3
        alpha (float): MADL alpha parameter. Default: 1000.0
        beta (float): MADL beta parameter. Default: 1.0
    """

    def __init__(self, quantiles, madl_weight: float = 0.3, alpha: float = 1000.0, beta: float = 1.0):
        super().__init__()
        self.quantile_loss = QuantileLoss(quantiles)
        self.madl_loss = MADLLoss(alpha, beta)
        self.madl_weight = madl_weight

        # Validate madl_weight
        if not 0.0 <= madl_weight <= 1.0:
            raise ValueError("MADL weight must be between 0.0 and 1.0")

    def forward(self, preds, target):
        """
        Forward pass of hybrid loss.

        Args:
            preds: Predicted returns (batch_size, seq_len, num_quantiles)
            target: Actual returns (batch_size, seq_len)

        Returns:
            torch.Tensor: Combined loss value
        """
        # Calculate quantile loss
        q_loss = self.quantile_loss(preds, target)

        # Calculate MADL loss (will handle quantile dimension internally)
        m_loss = self.madl_loss(preds, target)

        # Combine losses with weighting
        combined_loss = (1 - self.madl_weight) * q_loss + self.madl_weight * m_loss

        return combined_loss


def calculate_directional_accuracy(predictions, targets):
    """
    Calculate directional accuracy between predictions and targets.

    Args:
        predictions: Predicted values (numpy array or torch tensor)
        targets: Actual values (numpy array or torch tensor)

    Returns:
        float: Directional accuracy as a percentage (0-100)
    """
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.detach().cpu().numpy()

    # Handle quantile predictions by using median quantile
    if predictions.ndim == 3:
        median_idx = predictions.shape[-1] // 2
        predictions = predictions[:, :, median_idx]

    # Flatten arrays for easier calculation
    pred_flat = predictions.flatten()
    target_flat = targets.flatten()

    # Calculate directional agreement
    pred_signs = np.sign(pred_flat)
    target_signs = np.sign(target_flat)

    # Count agreements (excluding zero values)
    non_zero_mask = (target_flat != 0) & (pred_flat != 0)
    if np.sum(non_zero_mask) == 0:
        return 0.0

    agreements = pred_signs[non_zero_mask] == target_signs[non_zero_mask]
    directional_accuracy = np.mean(agreements) * 100

    return directional_accuracy


# Configuration constants for HybridLoss presets
DEFAULT_MADL_ALPHA = 1000.0  # Default alpha parameter for MADL (slope control around zero)
DEFAULT_MADL_BETA = 1.0  # Default beta parameter for MADL (return magnitude weighting)
DEFAULT_MADL_WEIGHT = 0.3  # Default weight for MADL in hybrid loss (0.0 to 1.0)

# Predefined HybridLoss configurations
HYBRID_QUANTILE_FOCUSED = 0.1  # Mostly quantile loss with slight MADL influence
HYBRID_BALANCED = 0.3  # Balanced approach (default)
HYBRID_MADL_FOCUSED = 0.6  # MADL-focused with uncertainty quantification
