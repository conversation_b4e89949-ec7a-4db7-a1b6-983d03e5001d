"""
Options Analysis Service Layer

This service integrates the complete options analysis functionality into the main system,
providing a clean interface for all options-related operations including:
- Cash-Secured Puts analysis
- Covered Calls analysis
- Iron Condors analysis
- Watchlist management
- Configuration management

Enhanced with Redis caching for improved performance on frequently accessed Watchlist & Configuration.
"""

import json
import os
import sqlite3
from datetime import datetime
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd

from src.utils.cache import get_vix_ticker, stock_cache
from src.utils.logger import get_logger

from .options_data import OptionsDataManager


def convert_timestamps_to_strings(obj):
    """
    Recursively convert pandas Timestamp objects and numpy data types to JSON-serializable formats.
    Handles timestamps, numpy integers, floats, booleans, and other non-serializable types.
    """
    if isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_timestamps_to_strings(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_timestamps_to_strings(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_timestamps_to_strings(item) for item in obj)
    elif isinstance(obj, (np.integer, np.int8, np.int16, np.int32, np.int64)):
        return int(obj)  # Convert numpy integers to Python int
    elif isinstance(obj, (np.floating, np.float16, np.float32, np.float64)):
        return float(obj)  # Convert numpy floats to Python float
    elif isinstance(obj, (np.bool_, np.bool8)):
        return bool(obj)  # Convert numpy booleans to Python bool
    elif isinstance(obj, np.ndarray):
        return obj.tolist()  # Convert numpy arrays to Python lists
    elif pd.isna(obj):
        return None
    elif hasattr(obj, "item") and callable(getattr(obj, "item")):
        # Catch any other numpy scalar types
        try:
            return obj.item()
        except (ValueError, TypeError):
            return str(obj)
    else:
        return obj


def get_db_connection():
    """Get database connection for options service."""
    db_path = os.path.join(os.path.dirname(__file__), "..", "..", "data", "stock_trading.db")
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn


class OptionsAnalysisService:
    """Main service class for options analysis functionality."""

    def __init__(self):
        self.stock_cache = stock_cache
        self.logger = get_logger()
        self.options_data_manager = OptionsDataManager()
        self.cache_ttl = 3600  # 1 hour cache TTL for user data

    def _get_watchlist_cache_key(self, account_id: int, watchlist_id: Optional[int] = None) -> str:
        """Generate cache key for watchlist data."""
        if watchlist_id:
            return f"watchlist:{account_id}:{watchlist_id}"
        return f"watchlists:{account_id}"

    def _get_strategy_config_cache_key(self, account_id: int, strategy_type: str) -> str:
        """Generate cache key for strategy configuration."""
        return f"strategy_config:{account_id}:{strategy_type}"

    def get_user_watchlist(self, account_id: int, watchlist_id: Optional[int] = None) -> Dict[str, Any]:
        """Get user's watchlist(s) for options analysis with Redis caching."""
        cache_key = self._get_watchlist_cache_key(account_id, watchlist_id)

        # Try to get from cache first
        cached_data = self.stock_cache.redis_client.get(cache_key)
        if cached_data:
            return json.loads(cached_data)

        # Cache miss - fetch from database
        conn = get_db_connection()
        try:
            if watchlist_id:
                query = """
                    SELECT watchlist_id, name, symbols, created_at, updated_at
                    FROM user_watchlists
                    WHERE account_id = ? AND watchlist_id = ?
                """
                result = conn.execute(query, (account_id, watchlist_id)).fetchone()
                if result:
                    watchlist_data = {
                        "watchlist_id": result[0],
                        "name": result[1],
                        "symbols": json.loads(result[2]),
                        "created_at": result[3],
                        "updated_at": result[4],
                    }
                    # Cache the result
                    self.stock_cache.redis_client.setex(cache_key, self.cache_ttl, json.dumps(watchlist_data))
                    return watchlist_data
                return None
            else:
                query = """
                    SELECT watchlist_id, name, symbols, created_at, updated_at
                    FROM user_watchlists
                    WHERE account_id = ?
                    ORDER BY created_at DESC
                """
                results = conn.execute(query, (account_id,)).fetchall()
                watchlists_data = [
                    {
                        "watchlist_id": row[0],
                        "name": row[1],
                        "symbols": json.loads(row[2]),
                        "created_at": row[3],
                        "updated_at": row[4],
                    }
                    for row in results
                ]
                # Cache the result
                self.stock_cache.redis_client.setex(cache_key, self.cache_ttl, json.dumps(watchlists_data))
                return watchlists_data
        finally:
            conn.close()

    def create_or_update_watchlist(
        self, account_id: int, symbols: List[str], name: str = "Default Watchlist", watchlist_id: Optional[int] = None
    ) -> int:
        """Create a new watchlist or update an existing one with cache invalidation."""
        conn = get_db_connection()
        try:
            symbols_json = json.dumps(symbols)
            current_time = datetime.now().isoformat()

            if watchlist_id:
                # Try to update existing watchlist
                cursor = conn.execute(
                    """
                    UPDATE user_watchlists
                    SET symbols = ?, name = ?, updated_at = ?
                    WHERE account_id = ? AND watchlist_id = ?
                """,
                    (symbols_json, name, current_time, account_id, watchlist_id),
                )

                # Check if the update affected any rows
                if cursor.rowcount > 0:
                    conn.commit()
                    # Invalidate cache for this watchlist and the user's watchlist list
                    self._invalidate_watchlist_cache(account_id, watchlist_id)
                    return watchlist_id
                else:
                    # Watchlist doesn't exist, create a new one
                    cursor = conn.execute(
                        """
                        INSERT INTO user_watchlists (account_id, name, symbols, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """,
                        (account_id, name, symbols_json, current_time, current_time),
                    )
                    conn.commit()
                    watchlist_id = cursor.lastrowid
                    # Invalidate cache
                    self._invalidate_watchlist_cache(account_id)
                    return watchlist_id
            else:
                # Create new watchlist
                cursor = conn.execute(
                    """
                    INSERT INTO user_watchlists (account_id, name, symbols, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """,
                    (account_id, name, symbols_json, current_time, current_time),
                )
                conn.commit()
                watchlist_id = cursor.lastrowid
                # Invalidate cache
                self._invalidate_watchlist_cache(account_id)
                return watchlist_id
        finally:
            conn.close()

    def delete_watchlist(self, watchlist_id: int) -> bool:
        """Delete a watchlist by ID with cache invalidation."""
        conn = get_db_connection()
        try:
            # Get account_id for cache invalidation
            account_query = "SELECT account_id FROM user_watchlists WHERE watchlist_id = ?"
            account_result = conn.execute(account_query, (watchlist_id,)).fetchone()

            if not account_result:
                return False

            account_id = account_result[0]

            # Delete the watchlist
            delete_query = "DELETE FROM user_watchlists WHERE watchlist_id = ?"
            cursor = conn.execute(delete_query, (watchlist_id,))
            conn.commit()

            if cursor.rowcount > 0:
                # Invalidate cache
                self._invalidate_watchlist_cache(account_id, watchlist_id)
                return True

            return False
        finally:
            conn.close()

    def _invalidate_watchlist_cache(self, account_id: int, watchlist_id: Optional[int] = None):
        """Invalidate watchlist cache entries."""
        try:
            # Invalidate specific watchlist cache
            if watchlist_id:
                specific_key = self._get_watchlist_cache_key(account_id, watchlist_id)
                self.stock_cache.redis_client.delete(specific_key)

            # Invalidate user's watchlist list cache
            list_key = self._get_watchlist_cache_key(account_id)
            self.stock_cache.redis_client.delete(list_key)
        except Exception as e:
            self.logger.warning(f"Error invalidating watchlist cache: {e}")

    def get_strategy_config(self, account_id: int, strategy_type: str) -> Dict[str, Any]:
        """Get strategy configuration for a user and strategy type with Redis caching."""
        cache_key = self._get_strategy_config_cache_key(account_id, strategy_type)

        # Try to get from cache first
        cached_config = self.stock_cache.redis_client.get(cache_key)
        if cached_config:
            return json.loads(cached_config)

        # Cache miss - fetch from database
        conn = get_db_connection()
        try:
            query = """
                SELECT config_data FROM strategy_configs
                WHERE account_id = ? AND strategy_type = ?
                ORDER BY is_default DESC, created_at DESC
                LIMIT 1
            """
            result = conn.execute(query, (account_id, strategy_type)).fetchone()
            if result:
                config_data = json.loads(result[0])
            else:
                # Return default configuration
                config_data = self._get_default_strategy_config(strategy_type)

            # Cache the result
            self.stock_cache.redis_client.setex(cache_key, self.cache_ttl, json.dumps(config_data))
            return config_data
        finally:
            conn.close()

    def save_strategy_config(
        self, account_id: int, strategy_type: str, config_data: Dict[str, Any], is_default: bool = False
    ) -> int:
        """Save strategy configuration for a user with cache invalidation."""
        conn = get_db_connection()
        try:
            config_json = json.dumps(config_data)
            current_time = datetime.now().isoformat()

            # Always create new config for versioning
            cursor = conn.execute(
                """
                INSERT INTO strategy_configs
                (account_id, strategy_type, config_data, is_default, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """,
                (account_id, strategy_type, config_json, int(is_default), current_time, current_time),
            )
            conn.commit()
            config_id = cursor.lastrowid

            # Invalidate cache for this strategy config
            self._invalidate_strategy_config_cache(account_id, strategy_type)

            return config_id
        finally:
            conn.close()

    def _invalidate_strategy_config_cache(self, account_id: int, strategy_type: str):
        """Invalidate strategy configuration cache."""
        try:
            cache_key = self._get_strategy_config_cache_key(account_id, strategy_type)
            self.stock_cache.redis_client.delete(cache_key)
        except Exception as e:
            self.logger.warning(f"Error invalidating strategy config cache: {e}")

    def _get_default_strategy_config(self, strategy_type: str) -> Dict[str, Any]:
        """
        Get optimized default configuration for options strategies.

        Configurations are optimized for:
        - Higher win rates through conservative targeting
        - Improved risk management with appropriate buffers
        - Market condition awareness through adaptive parameters
        - Liquidity requirements for reliable execution

        Args:
            strategy_type: One of 'cash_secured_puts', 'covered_calls', 'iron_condors'

        Returns:
            Dict containing strategy-specific configuration parameters
        """
        # Get current market volatility for adaptive configurations
        market_volatility = self._get_current_market_volatility()

        default_configs = {
            "cash_secured_puts": {
                # Time to Expiration - Optimized for theta decay
                "min_dte": 30,
                "max_dte": 45,
                # Return Requirements - Conservative but profitable
                "min_annual_roi": 0.10 if market_volatility < 0.25 else 0.08,  # Lower in high vol
                # Risk Management - Enhanced safety margins
                "max_delta": 0.25,  # Conservative delta targeting
                "min_buffer_percent": 0.05,  # 5% minimum safety buffer
                # Liquidity Requirements - Ensure reliable execution
                "min_open_interest": 500,
                "min_volume": 50,
                "min_bid_ask_spread_ratio": 0.15,  # Max 15% spread
                # Market Condition Filters
                "min_implied_volatility": 0.20,
                "max_implied_volatility": 0.80,
                # Selection Criteria
                "sort_by": "winRateScore",  # Prioritize probability of success
                "max_candidates": 10,
            },
            "covered_calls": {
                # Time to Expiration - Faster theta decay for income
                "min_dte": 25,
                "max_dte": 35,
                # Return Requirements
                "min_annual_roi": 0.06 if market_volatility < 0.25 else 0.05,
                # Risk Management - Balance income vs assignment risk
                "max_delta": 0.30,
                "min_upside_buffer": 0.03,  # 3% minimum upside protection
                # Liquidity Requirements
                "min_open_interest": 300,
                "min_volume": 30,
                "min_bid_ask_spread_ratio": 0.15,
                # Market Condition Filters
                "min_implied_volatility": 0.18,
                "max_implied_volatility": 0.70,
                # Selection Criteria
                "sort_by": "totalReturnIfCalled",  # Maximize total return potential
                "max_candidates": 10,
            },
            "iron_condors": {
                # Time to Expiration - Optimal for neutral strategies
                "min_dte": 25,
                "max_dte": 35,
                # Return Requirements
                "min_annual_roi": 0.15 if market_volatility < 0.30 else 0.12,
                # Strike Selection - Conservative delta targeting
                "target_delta_short_put": 0.15,
                "target_delta_short_call": 0.15,
                "delta_tolerance": 0.05,  # Allow 5% delta variance
                # Wing Configuration - Balanced risk/reward
                "min_wing_width": 5,
                "max_wing_width": 10,
                # Risk Management
                "min_probability_of_profit": 0.70,  # 70% minimum PoP
                "max_loss_to_profit_ratio": 3.0,  # 1:3 risk/reward max
                "min_distance_from_current": 0.08,  # 8% minimum distance
                "max_distance_from_current": 0.20,  # 20% maximum distance
                # Liquidity Requirements - Higher for complex strategies
                "min_open_interest": 200,
                "min_volume": 20,
                "min_bid_ask_spread_ratio": 0.20,
                # Market Condition Filters
                "min_implied_volatility": 0.22,
                "max_implied_volatility": 0.60,
                # Selection Criteria
                "sort_by": "probabilityOfProfit",
                "max_candidates": 5,
            },
        }

        # Apply market condition adjustments
        config = default_configs.get(strategy_type, {})
        if config:
            config = self._apply_market_condition_adjustments(config, strategy_type, market_volatility)

        return config

    def _get_current_market_volatility(self) -> float:
        """
        Get current market volatility using VIX data from yfinance.

        Returns:
            Current market volatility as a decimal (e.g., 0.25 for 25%)
        """
        try:
            vix = get_vix_ticker()
            vix_data = vix.history(period="1d")

            if not vix_data.empty:
                current_vix = float(vix_data["Close"].iloc[-1])
                volatility = current_vix / 100.0  # Convert percentage to decimal
                self.logger.info(f"Successfully fetched VIX data: {volatility:.2%} (VIX: {current_vix:.1f})")
                return volatility

            # If VIX fetch fails, use a reasonable default
            default_volatility = 0.20  # 20% default volatility
            self.logger.warning(f"Could not fetch VIX data, using default: {default_volatility:.2%}")
            return default_volatility

        except Exception as e:
            self.logger.warning(f"Error fetching VIX data: {e}, using default volatility")
            return 0.20  # Safe default

    def _apply_market_condition_adjustments(
        self, config: Dict[str, Any], strategy_type: str, market_volatility: float
    ) -> Dict[str, Any]:
        """
        Apply market condition-based adjustments to strategy configurations.

        Args:
            config: Base configuration dictionary
            strategy_type: Type of options strategy
            market_volatility: Current market volatility level

        Returns:
            Adjusted configuration dictionary
        """
        adjusted_config = config.copy()

        # High volatility adjustments (VIX > 30%)
        if market_volatility > 0.30:
            self.logger.info(f"Applying high volatility adjustments for {strategy_type}")

            # Reduce ROI requirements in high vol (easier to achieve)
            if "min_annual_roi" in adjusted_config:
                adjusted_config["min_annual_roi"] *= 0.8

            # Increase safety buffers
            if "min_buffer_percent" in adjusted_config:
                adjusted_config["min_buffer_percent"] *= 1.2
            if "min_upside_buffer" in adjusted_config:
                adjusted_config["min_upside_buffer"] *= 1.2

            # Tighten delta requirements for safety
            if "max_delta" in adjusted_config:
                adjusted_config["max_delta"] *= 0.9
            if "target_delta_short_put" in adjusted_config:
                adjusted_config["target_delta_short_put"] *= 0.9
            if "target_delta_short_call" in adjusted_config:
                adjusted_config["target_delta_short_call"] *= 0.9

        # Low volatility adjustments (VIX < 15%)
        elif market_volatility < 0.15:
            self.logger.info(f"Applying low volatility adjustments for {strategy_type}")

            # Increase ROI requirements in low vol (harder to achieve)
            if "min_annual_roi" in adjusted_config:
                adjusted_config["min_annual_roi"] *= 1.2

            # Can be slightly more aggressive with deltas
            if "max_delta" in adjusted_config:
                adjusted_config["max_delta"] *= 1.1
            if "target_delta_short_put" in adjusted_config:
                adjusted_config["target_delta_short_put"] *= 1.1
            if "target_delta_short_call" in adjusted_config:
                adjusted_config["target_delta_short_call"] *= 1.1

        # Apply strategy-specific adjustments
        if strategy_type == "iron_condors":
            # Iron condors benefit from higher volatility
            if market_volatility > 0.25:
                adjusted_config["min_wing_width"] = max(adjusted_config.get("min_wing_width", 5), 7)
                adjusted_config["max_wing_width"] = min(adjusted_config.get("max_wing_width", 10), 8)

        return adjusted_config
