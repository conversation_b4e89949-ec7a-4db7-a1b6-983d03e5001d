"""
Options Data Fetching and Processing

Migrated from the standalone options analysis system.
Handles real-time options data acquisition, caching, and market analysis.
"""

import json
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from yahooquery import Ticker

from src.core.scheduler import is_market_open_today
from src.utils.cache import get_vix_ticker, stock_cache
from src.utils.logger import get_logger


class OptionsDataManager:
    """Manages options data fetching, caching, and market analysis."""

    def __init__(self):
        self.logger = get_logger()
        self.stock_cache = stock_cache

    def validate_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Validate a symbol and check if it likely supports options trading.
        Returns validation info including whether options might be available.
        """
        validation_result = {
            "symbol": symbol,
            "is_valid": False,
            "has_market_data": False,
            "likely_has_options": False,
            "symbol_type": "unknown",
            "exchange": None,
            "market_cap": None,
            "error": None,
        }

        try:
            # Use yahooquery for quick validation
            ticker = Ticker(symbol)
            summary = ticker.summary_detail

            if isinstance(summary, dict) and symbol in summary:
                symbol_data = summary[symbol]
                validation_result["is_valid"] = True
                validation_result["has_market_data"] = True

                # Extract market data
                market_cap = symbol_data.get("marketCap")
                if market_cap:
                    validation_result["market_cap"] = market_cap
                    # Large cap stocks (>$1B) are more likely to have options
                    validation_result["likely_has_options"] = market_cap > 1_000_000_000

                # Check if it's an ETF or stock
                if symbol.endswith("LL") or "leveraged" in str(symbol_data).lower():
                    validation_result["symbol_type"] = "leveraged_etf"
                    # Leveraged ETFs may or may not have options
                    validation_result["likely_has_options"] = market_cap and market_cap > 100_000_000
                elif market_cap and market_cap > 10_000_000_000:
                    validation_result["symbol_type"] = "large_cap_stock"
                    validation_result["likely_has_options"] = True
                elif market_cap and market_cap > 1_000_000_000:
                    validation_result["symbol_type"] = "mid_cap_stock"
                    validation_result["likely_has_options"] = True
                else:
                    validation_result["symbol_type"] = "small_cap_or_etf"
                    validation_result["likely_has_options"] = False

        except Exception as e:
            validation_result["error"] = str(e)
            self.logger.warning(f"Symbol validation failed for {symbol}: {str(e)}")

        return validation_result

    def fetch_data_for_batch(self, symbols: List[str], config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, float]]:
        """
        Fetch options and price data for a batch of symbols.
        Migrated from utils.py with enhanced error handling.
        """
        batch_size = config.get("data_acquisition", {}).get("batch_size", 20)

        all_options = []
        all_prices = {}

        self.logger.info(f"Fetching data for {len(symbols)} symbols in batch")

        # Validate symbols first and provide early feedback
        validation_results = {}
        for symbol in symbols:
            validation_results[symbol] = self.validate_symbol(symbol)

        # Log validation results
        valid_symbols = [s for s, v in validation_results.items() if v["is_valid"]]
        invalid_symbols = [s for s, v in validation_results.items() if not v["is_valid"]]
        unlikely_options = [s for s, v in validation_results.items() if v["is_valid"] and not v["likely_has_options"]]

        if invalid_symbols:
            self.logger.warning(f"Invalid symbols detected: {invalid_symbols}")
        if unlikely_options:
            self.logger.info(f"Symbols unlikely to have options: {unlikely_options}")
        if valid_symbols:
            self.logger.info(f"Valid symbols to process: {valid_symbols}")

        # Fetch fresh data for all symbols
        symbols_to_fetch = symbols
        self.logger.info(f"Fetching fresh data for {len(symbols_to_fetch)} symbols")

        # 优化：先批量获取所有价格数据，然后处理期权数据
        try:
            self.logger.info(f"Batch fetching prices for {len(symbols_to_fetch)} symbols")
            all_prices = self._fetch_current_prices_optimized(symbols_to_fetch)
            self.logger.info(f"Successfully fetched prices for {len(all_prices)} symbols")
        except Exception as e:
            self.logger.error(f"Error in batch price fetch: {str(e)}")
            all_prices = {}

        # Process in smaller batches to avoid rate limits for options data
        for i in range(0, len(symbols_to_fetch), batch_size):
            batch = symbols_to_fetch[i : i + batch_size]
            self.logger.info(f"Processing options batch {i // batch_size + 1}: {batch}")

            try:
                # Fetch options data
                options_df = self._fetch_options_for_symbols(batch)
                if not options_df.empty:
                    all_options.append(options_df)

                # 如果批量价格获取失败，回退到单批次获取
                if not all_prices:
                    batch_prices = self._fetch_current_prices(batch)
                    all_prices.update(batch_prices)

                # Rate limiting
                if i + batch_size < len(symbols_to_fetch):
                    time.sleep(1)  # 1 second delay between batches

            except Exception as e:
                self.logger.error(f"Error fetching data for batch {batch}: {str(e)}")
                continue

        # Combine all options data
        combined_options = pd.concat(all_options, ignore_index=True) if all_options else pd.DataFrame()

        # Add current prices to options data
        if not combined_options.empty and all_prices:
            combined_options["currentPrice"] = combined_options["symbol"].map(all_prices)
            combined_options = combined_options.dropna(subset=["currentPrice"])

        # Provide detailed feedback about results
        if combined_options.empty:
            successful_symbols = []
            failed_symbols = symbols
            error_msg = f"No options data retrieved for symbols: {failed_symbols}. "
            error_msg += "Possible reasons: (1) Symbols don't support options trading, "
            error_msg += "(2) Network connectivity issues, (3) Invalid symbols, or (4) API rate limiting."
            self.logger.error(error_msg)
        else:
            successful_symbols = combined_options["symbol"].unique().tolist() if "symbol" in combined_options.columns else []
            failed_symbols = [s for s in symbols if s not in successful_symbols]

            self.logger.info(
                f"Successfully fetched {len(combined_options)} options records for {len(successful_symbols)} symbols: {successful_symbols}"
            )
            if failed_symbols:
                self.logger.warning(f"Failed to fetch options data for symbols: {failed_symbols}")

        return combined_options, all_prices

    def _fetch_options_for_symbols(self, symbols: List[str]) -> pd.DataFrame:
        """Fetch options data for a list of symbols."""
        all_options = []

        for symbol in symbols:
            try:
                self.logger.debug(f"Fetching options for {symbol}")
                options_df = self._fetch_single_symbol_options(symbol)
                if not options_df.empty:
                    all_options.append(options_df)

            except Exception as e:
                self.logger.warning(f"Failed to fetch options for {symbol}: {str(e)}")
                continue

        return pd.concat(all_options, ignore_index=True) if all_options else pd.DataFrame()

    def _fetch_single_symbol_options(self, symbol: str) -> pd.DataFrame:
        """Fetch options data for a single symbol using yahooquery exclusively."""
        return self._fetch_single_symbol_options_yahooquery(symbol)

    def _fetch_single_symbol_options_yahooquery(self, symbol: str) -> pd.DataFrame:
        """Fetch options data using yahooquery with enhanced DataFrame handling."""
        try:
            ticker = Ticker(symbol)

            # First, verify the symbol exists and get basic info
            try:
                summary = ticker.summary_detail
                if isinstance(summary, dict) and symbol in summary:
                    self.logger.info(f"Symbol {symbol} found with market data")
                else:
                    self.logger.warning(f"Symbol {symbol} not found or has no market data")
                    return pd.DataFrame()
            except Exception as e:
                self.logger.warning(f"Could not verify symbol {symbol}: {str(e)}")

            # Get option expiration dates
            option_chain = ticker.option_chain

            # Handle different response types from yahooquery
            if option_chain is None:
                self.logger.warning(f"No options chain response for {symbol}")
                return pd.DataFrame()

            # NEW: Handle DataFrame response (some symbols return DataFrame instead of dict)
            if isinstance(option_chain, pd.DataFrame):
                self.logger.info(f"Received DataFrame response for {symbol}, converting to expected format")
                self.logger.info(f"DataFrame columns for {symbol}: {list(option_chain.columns)}")
                self.logger.info(f"DataFrame shape for {symbol}: {option_chain.shape}")

                try:
                    if not option_chain.empty:
                        # Check if DataFrame has the expected structure
                        if "optionType" in option_chain.columns:
                            calls_data = option_chain[option_chain["optionType"] == "calls"].to_dict("records")
                            puts_data = option_chain[option_chain["optionType"] == "puts"].to_dict("records")
                            symbol_data = {"calls": calls_data, "puts": puts_data}
                            self.logger.info(
                                f"Found optionType column for {symbol}: {len(calls_data)} calls, {len(puts_data)} puts"
                            )
                        else:
                            # Infer option type from contract symbols or other data
                            self.logger.info(
                                f"No optionType column found for {symbol}, attempting to infer from contract data"
                            )
                            symbol_data = self._infer_option_types_from_dataframe(option_chain, symbol)

                            if symbol_data and ("calls" in symbol_data or "puts" in symbol_data):
                                calls_count = len(symbol_data.get("calls", []))
                                puts_count = len(symbol_data.get("puts", []))
                                self.logger.info(
                                    f"Successfully inferred option types for {symbol}: {calls_count} calls, {puts_count} puts"
                                )
                            else:
                                self.logger.warning(f"Could not infer option types for {symbol} from available data")
                                return pd.DataFrame()
                    else:
                        self.logger.warning(f"Empty DataFrame received for {symbol}")
                        return pd.DataFrame()
                except Exception as e:
                    self.logger.error(f"Error processing DataFrame response for {symbol}: {str(e)}")
                    return pd.DataFrame()

            # Handle dict response (normal case)
            elif isinstance(option_chain, dict):
                # Check if it's an error response
                if "error" in str(option_chain).lower():
                    self.logger.warning(f"Options chain error for {symbol}: {option_chain}")
                    return pd.DataFrame()

                # Check if symbol exists in the response
                if symbol not in option_chain:
                    self.logger.warning(f"No options data available for {symbol} - symbol may not have options trading")
                    return pd.DataFrame()

                symbol_data = option_chain[symbol]

                # Check if the response contains error information
                if isinstance(symbol_data, dict) and "error" in symbol_data:
                    self.logger.warning(f"Options error for {symbol}: {symbol_data['error']}")
                    return pd.DataFrame()

                if not isinstance(symbol_data, dict) or ("calls" not in symbol_data and "puts" not in symbol_data):
                    self.logger.warning(f"No calls or puts data for {symbol} - options may not be available for this symbol")
                    return pd.DataFrame()

            else:
                self.logger.warning(f"Unexpected option_chain response type for {symbol}: {type(option_chain)}")
                return pd.DataFrame()

            options_list = []

            # Process calls
            if "calls" in symbol_data and symbol_data["calls"] is not None:
                try:
                    calls_df = pd.DataFrame(symbol_data["calls"])
                    if not calls_df.empty:
                        calls_df["optionType"] = "calls"
                        calls_df["symbol"] = symbol
                        options_list.append(calls_df)
                        self.logger.info(f"Found {len(calls_df)} call options for {symbol}")
                except Exception as e:
                    self.logger.warning(f"Error processing calls for {symbol}: {str(e)}")

            # Process puts
            if "puts" in symbol_data and symbol_data["puts"] is not None:
                try:
                    puts_df = pd.DataFrame(symbol_data["puts"])
                    if not puts_df.empty:
                        puts_df["optionType"] = "puts"
                        puts_df["symbol"] = symbol
                        options_list.append(puts_df)
                        self.logger.info(f"Found {len(puts_df)} put options for {symbol}")
                except Exception as e:
                    self.logger.warning(f"Error processing puts for {symbol}: {str(e)}")

            if not options_list:
                self.logger.warning(f"No valid options data found for {symbol} after processing")
                return pd.DataFrame()

            # Combine calls and puts
            combined_df = pd.concat(options_list, ignore_index=True)

            # Standardize column names and add calculated fields
            combined_df = self._standardize_options_data(combined_df)

            self.logger.info(f"Successfully processed {len(combined_df)} total options for {symbol} via yahooquery")
            return combined_df

        except Exception as e:
            self.logger.error(f"Error fetching options for {symbol} via yahooquery: {str(e)}")
            return pd.DataFrame()

    def _standardize_options_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize options data format and add calculated fields."""
        if df.empty:
            return df

        # Ensure required columns exist
        required_columns = ["strike", "bid", "ask", "expiration", "impliedVolatility"]
        for col in required_columns:
            if col not in df.columns:
                df[col] = np.nan

        # Calculate DTE (Days to Expiration)
        if "expiration" in df.columns:
            df["expiration"] = pd.to_datetime(df["expiration"])
            df["dte"] = (df["expiration"] - datetime.now()).dt.days

            # Filter out expired options and very short-term options
            df = df[df["dte"] > 0]

        # Clean numeric columns - use .loc to avoid SettingWithCopyWarning
        numeric_columns = ["strike", "bid", "ask", "impliedVolatility", "volume", "openInterest"]
        for col in numeric_columns:
            if col in df.columns:
                df.loc[:, col] = pd.to_numeric(df[col], errors="coerce")

        # Analyze liquidity before filtering
        total_options = len(df)
        zero_bid_ask = len(df[(df["bid"] <= 0) | (df["ask"] <= 0)])
        symbol = df["symbol"].iloc[0] if "symbol" in df.columns and not df.empty else "Unknown"

        # Check market status to determine filtering behavior
        try:
            market_open = is_market_open_today()
            market_status = "Market is open" if market_open else "Market is closed"
        except Exception as e:
            self.logger.debug(f"Could not determine market status: {e}")
            market_status = "Market status unknown"
            market_open = True  # Default to filtering when status unknown

        # Filter out options with no bid/ask data only if market is open
        if market_open:
            self.logger.info(f"Market is open - filtering out options with zero bid/ask for {symbol}")
            df = df.dropna(subset=["bid", "ask"])
            df = df[(df["bid"] > 0) & (df["ask"] > 0)]

            if df.empty:
                self.logger.warning(
                    f"All options filtered out for {symbol} due to zero bid/ask prices - low liquidity symbol ({market_status})"
                )
                return df
        else:
            self.logger.info(f"Market is closed - keeping all options including zero bid/ask for {symbol}")
            # When market is closed, be more lenient - only remove rows where both bid AND ask are NaN
            df = df[~(df["bid"].isna() & df["ask"].isna())]

        # Calculate mid price and spread metrics
        # Handle NaN values in bid/ask differently based on market status
        if not market_open:
            # When market is closed, fill NaN values with 0 to be more lenient
            df["bid"] = df["bid"].fillna(0.0)
            df["ask"] = df["ask"].fillna(0.0)

        df["mid"] = (df["bid"] + df["ask"]) / 2
        df["spread"] = df["ask"] - df["bid"]

        # Handle spread percentage calculation to avoid division by zero
        # When market is closed, zero bid/ask should have spread_pct = 0, not inf
        df["spread_pct"] = np.where(
            df["mid"] > 0, df["spread"] / df["mid"], np.where((df["bid"] == 0) & (df["ask"] == 0), 0.0, np.inf)
        )

        # Enhanced filtering with more lenient thresholds for low-liquidity symbols
        min_bid = 0.01  # Reduced from 0.05 for low-priced options
        max_spread_pct = 0.8  # More lenient spread threshold

        # Apply filters only if market is open
        before_filter = len(df)
        spread_filtered = 0

        if market_open:
            # Apply strict filtering when market is open
            df = df[df["spread_pct"] <= max_spread_pct]
            spread_filtered = before_filter - len(df)

            df = df[df["bid"] >= min_bid]
        else:
            # When market is closed, only filter out infinite spread percentages
            df = df[np.isfinite(df["spread_pct"])]
            spread_filtered = before_filter - len(df)

        final_count = len(df)

        # Log filtering results
        if spread_filtered > 0:
            if market_open:
                self.logger.info(
                    f"Filtered out {spread_filtered} options for {symbol} due to wide spreads (>{max_spread_pct * 100}%) - market is open"
                )
            else:
                self.logger.info(f"Filtered out {spread_filtered} options for {symbol} due to invalid data - market is closed")

        if final_count > 0:
            filter_reason = "strict filtering (market open)" if market_open else "minimal filtering (market closed)"
            self.logger.info(
                f"Options filtering summary for {symbol}: {total_options} → {final_count} ({final_count / total_options * 100:.1f}% retained) - {filter_reason}"
            )

        return df

    def _fetch_current_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Fetch current stock prices for symbols using the enhanced stock cache."""
        # Use the stock cache for better performance and caching
        return self.stock_cache.get_current_prices(symbols)

    def _fetch_current_prices_optimized(self, symbols: List[str]) -> Dict[str, float]:
        """Optimized batch price fetching using portfolio prices method."""
        try:
            # Use the optimized portfolio prices method for better batch performance
            return self.stock_cache.get_portfolio_prices(symbols)
        except Exception as e:
            self.logger.error(f"Error in optimized price fetch: {e}")
            # Fallback to regular method
            return self._fetch_current_prices(symbols)

    def detect_volatility_regime(self, symbols: List[str]) -> str:
        """
        Detect current market volatility regime.
        Migrated from utils.py with simplified implementation.
        """
        try:
            vix = get_vix_ticker()
            vix_data = vix.history(period="5d")

            if vix_data.empty:
                return "Normal"

            current_vix = vix_data["Close"].iloc[-1]

            if current_vix > 30:
                return "High Volatility"
            elif current_vix < 15:
                return "Low Volatility"
            else:
                return "Normal"

        except Exception as e:
            self.logger.warning(f"Error detecting volatility regime: {str(e)}")
            return "Normal"

    def calculate_market_stress_indicator(self, symbols: List[str]) -> float:
        """
        Calculate market stress indicator (0-100 scale).
        Migrated from utils.py with simplified implementation.
        """
        try:
            vix = get_vix_ticker()
            vix_data = vix.history(period="1d")

            if vix_data.empty:
                return 50.0  # Neutral

            current_vix = vix_data["Close"].iloc[-1]

            # Convert VIX to 0-100 stress scale
            # VIX of 10 = 0 stress, VIX of 50+ = 100 stress
            stress_level = min(100, max(0, (current_vix - 10) * 2.5))

            return float(stress_level)

        except Exception as e:
            self.logger.warning(f"Error calculating market stress: {str(e)}")
            return 50.0  # Neutral fallback

    def _infer_option_types_from_dataframe(self, df: pd.DataFrame, symbol: str) -> Dict[str, List[Dict]]:
        """
        Infer option types (calls vs puts) from DataFrame when optionType column is missing.
        Uses contract symbols, strike prices, and other available data.
        """
        try:
            # Early validation - check if DataFrame is empty
            if df.empty:
                self.logger.warning(f"Empty DataFrame provided for {symbol}")
                return {"calls": [], "puts": []}

            self.logger.info(f"Processing {len(df)} options records for {symbol}")

            # Method 1: Infer from contract symbols
            if "contractSymbol" in df.columns and not df["contractSymbol"].isna().all():
                self.logger.info(f"Attempting to infer option types from contractSymbol for {symbol}")

                # Sample a few contract symbols to understand the pattern
                sample_contracts = df["contractSymbol"].dropna().head(10).tolist()
                self.logger.info(f"Sample contract symbols for {symbol}: {sample_contracts}")

                # Enhanced pattern matching for various contract symbol formats
                import re

                # Multiple patterns to handle different broker formats
                patterns = {
                    "standard": r"[A-Z]+\d{6}[CP]\d+",  # AAPL240216C00150000
                    "alternative": r"[A-Z]+_\d{6}[CP]\d+",  # AAPL_240216C150
                    "simple": r"[A-Z]+[CP]\d+",  # AAPLC150
                }

                # Handle multi-index DataFrames by resetting index if necessary
                original_index = df.index
                is_multi_index = isinstance(df.index, pd.MultiIndex)

                if is_multi_index:
                    self.logger.debug(f"Detected multi-index DataFrame for {symbol}: {df.index.names}")
                    # Reset index to get simple integer index for processing
                    df_working = df.reset_index(drop=False)
                    working_index = df_working.index
                else:
                    df_working = df
                    working_index = df.index

                # Create boolean masks with proper DataFrame index alignment
                calls_mask = pd.Series(False, index=working_index, dtype=bool)
                puts_mask = pd.Series(False, index=working_index, dtype=bool)

                self.logger.debug(
                    f"Created boolean masks for {symbol} with {len(calls_mask)} entries (multi-index: {is_multi_index})"
                )

                # Iterate through DataFrame rows with comprehensive error handling
                processed_count = 0
                calls_found = 0
                puts_found = 0

                for i, idx in enumerate(working_index):
                    try:
                        # Use iloc for safe access regardless of index type
                        contract = df_working.iloc[i]["contractSymbol"] if "contractSymbol" in df_working.columns else None
                        if pd.isna(contract) or contract == "":
                            continue

                        contract_str = str(contract).upper().strip()
                        processed_count += 1

                        # Primary logic: Look for 'C' or 'P' indicators
                        is_call = False
                        is_put = False

                        # Method 1: Simple C/P detection with position awareness
                        if "C" in contract_str and "P" not in contract_str:
                            is_call = True
                        elif "P" in contract_str and "C" not in contract_str:
                            is_put = True
                        elif "C" in contract_str and "P" in contract_str:
                            # Both C and P present - use pattern matching for disambiguation
                            for pattern_name, pattern in patterns.items():
                                if re.search(pattern.replace("[CP]", "C"), contract_str):
                                    is_call = True
                                    break
                                elif re.search(pattern.replace("[CP]", "P"), contract_str):
                                    is_put = True
                                    break

                        # Method 2: Regex pattern matching as fallback
                        if not is_call and not is_put:
                            for pattern_name, pattern in patterns.items():
                                call_match = re.search(pattern.replace("[CP]", "C"), contract_str)
                                put_match = re.search(pattern.replace("[CP]", "P"), contract_str)

                                if call_match:
                                    is_call = True
                                    break
                                elif put_match:
                                    is_put = True
                                    break

                        # Update masks using safe iloc assignment
                        if is_call:
                            calls_mask.iloc[i] = True
                            calls_found += 1
                        elif is_put:
                            puts_mask.iloc[i] = True
                            puts_found += 1

                    except Exception as row_error:
                        self.logger.warning(
                            f"Error processing contract symbol at row {i} (index {idx}) for {symbol}: {row_error}"
                        )
                        continue

                self.logger.info(
                    f"Processed {processed_count} contracts for {symbol}: {calls_found} calls, {puts_found} puts identified"
                )

                # Validate boolean masks before using them
                if not isinstance(calls_mask, pd.Series) or not isinstance(puts_mask, pd.Series):
                    raise ValueError("Boolean masks are not valid pandas Series")

                # Use explicit boolean checks to avoid Series ambiguity
                has_calls = calls_mask.any() if not calls_mask.empty else False
                has_puts = puts_mask.any() if not puts_mask.empty else False

                self.logger.debug(f"Boolean mask validation for {symbol}: has_calls={has_calls}, has_puts={has_puts}")

                # Create filtered DataFrames with proper error handling
                calls_df = pd.DataFrame()
                puts_df = pd.DataFrame()

                if has_calls:
                    try:
                        # Filter using the working DataFrame and then restore original index if needed
                        calls_df = df_working.loc[calls_mask].copy()
                        if not calls_df.empty:
                            calls_df["optionType"] = "calls"
                            # If we had a multi-index, restore the original structure
                            if is_multi_index and "level_0" in calls_df.columns:
                                # Remove the reset index columns and restore original index structure
                                index_cols = [col for col in calls_df.columns if col.startswith("level_")]
                                if index_cols:
                                    calls_df = calls_df.drop(columns=index_cols)
                        self.logger.debug(f"Created calls DataFrame for {symbol} with {len(calls_df)} records")
                    except Exception as calls_error:
                        self.logger.error(f"Error creating calls DataFrame for {symbol}: {calls_error}")
                        calls_df = pd.DataFrame()

                if has_puts:
                    try:
                        # Filter using the working DataFrame and then restore original index if needed
                        puts_df = df_working.loc[puts_mask].copy()
                        if not puts_df.empty:
                            puts_df["optionType"] = "puts"
                            # If we had a multi-index, restore the original structure
                            if is_multi_index and "level_0" in puts_df.columns:
                                # Remove the reset index columns and restore original index structure
                                index_cols = [col for col in puts_df.columns if col.startswith("level_")]
                                if index_cols:
                                    puts_df = puts_df.drop(columns=index_cols)
                        self.logger.debug(f"Created puts DataFrame for {symbol} with {len(puts_df)} records")
                    except Exception as puts_error:
                        self.logger.error(f"Error creating puts DataFrame for {symbol}: {puts_error}")
                        puts_df = pd.DataFrame()

                self.logger.info(f"Contract symbol inference for {symbol}: {len(calls_df)} calls, {len(puts_df)} puts")

                # Return results if we found any valid options
                if len(calls_df) > 0 or len(puts_df) > 0:
                    return {
                        "calls": calls_df.to_dict("records") if len(calls_df) > 0 else [],
                        "puts": puts_df.to_dict("records") if len(puts_df) > 0 else [],
                    }

            # Method 2: Fallback - split data in half with enhanced safety
            self.logger.warning(f"Using fallback method for {symbol} - splitting data roughly in half")

            if len(df) == 0:
                self.logger.warning(f"Cannot split empty DataFrame for {symbol}")
                return {"calls": [], "puts": []}

            if len(df) == 1:
                # Single record - assume it's a call for simplicity
                single_df = df.copy()
                single_df["optionType"] = "calls"
                self.logger.info(f"Single record fallback for {symbol}: treating as call")
                return {"calls": single_df.to_dict("records"), "puts": []}

            # Split data safely
            mid_point = len(df) // 2

            try:
                calls_df = df.iloc[:mid_point].copy()
                puts_df = df.iloc[mid_point:].copy()

                # Only set optionType if DataFrames are not empty
                if not calls_df.empty:
                    calls_df["optionType"] = "calls"
                if not puts_df.empty:
                    puts_df["optionType"] = "puts"

                self.logger.info(f"Fallback split for {symbol}: {len(calls_df)} calls, {len(puts_df)} puts")

                return {
                    "calls": calls_df.to_dict("records") if not calls_df.empty else [],
                    "puts": puts_df.to_dict("records") if not puts_df.empty else [],
                }

            except Exception as fallback_error:
                self.logger.error(f"Error in fallback method for {symbol}: {fallback_error}")
                return {"calls": [], "puts": []}

        except Exception as e:
            self.logger.error(f"Error inferring option types for {symbol}: {str(e)}")
            import traceback

            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return {"calls": [], "puts": []}

    def get_market_conditions(self, symbols: List[str]) -> Dict[str, Any]:
        """Get comprehensive market conditions analysis."""
        return {
            "volatility_regime": self.detect_volatility_regime(symbols),
            "stress_indicator": self.calculate_market_stress_indicator(symbols),
            "timestamp": datetime.now().isoformat(),
        }

    def refresh_options_data(self, symbols: List[str]) -> Tuple[pd.DataFrame, Dict[str, float]]:
        """
        Refresh options data for specified symbols.

        Args:
            symbols: List of stock symbols to refresh

        Returns:
            Tuple of (options_dataframe, current_prices)
        """
        # Fetch fresh data
        config = {"data_acquisition": {"batch_size": min(10, len(symbols))}}

        return self.fetch_data_for_batch(symbols, config)
